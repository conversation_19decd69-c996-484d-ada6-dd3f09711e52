[font]
name = "Microsoft YaHei"
size = 20

[layout]
vertical = false

[color]
clip = "#0078D7"
background = "#FAFAFA"
highlight = "#E8E8FF"
index = "#A0A0A0"
candidate = "black"
highlighted = "black"

[behavior]
toggle = "Ctrl"
cjk_space = true

[engine]
# 引擎类型: "simple" 或 "rime"
type = "rime"
# RIME配置目录（可选）
rime_config_dir = ""

[ai_completion]
# 是否启用AI文本补全功能
enabled = false
# API端点（支持OpenAI兼容接口，如Ollama）
api_endpoint = "http://localhost:11434/v1/chat/completions"
# API密钥（可选，本地Ollama不需要）
api_key = ""
# 使用的模型名称
model = "qwen2.5:0.5b"
# 最大生成token数
max_tokens = 100
# 生成温度（0.0-1.0）
temperature = 0.7
# API调用超时时间（秒）
timeout_seconds = 10
# 空闲触发延迟（毫秒）
trigger_delay_ms = 2000
# 最大上下文长度（字符数）
max_context_length = 500
# 是否启用Ctrl键触发
trigger_on_ctrl = true
# 是否启用空闲触发
trigger_on_idle = true
