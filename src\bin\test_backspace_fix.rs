use chinese_ime::engine::RimeEngine;
use log::{info, error};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    
    println!("=== Composition状态下Backspace测试 ===\n");
    
    // 创建RIME引擎
    println!("1. 初始化RIME引擎...");
    let engine = RimeEngine::new().map_err(|e| {
        eprintln!("无法创建RIME引擎: {:?}", e);
        e
    })?;
    
    if !engine.is_initialized() {
        eprintln!("RIME引擎初始化失败");
        return Ok(());
    }
    
    println!("✓ RIME引擎初始化成功\n");
    
    // 测试场景1：正常输入然后backspace
    println!("2. 测试场景1：输入'ni'然后按backspace");
    test_input_backspace_scenario(&engine, vec!['n' as i32, 'i' as i32], vec![0x08])?;
    
    // 测试场景2：输入多个字符然后连续backspace
    println!("\n3. 测试场景2：输入'nihao'然后连续按5次backspace");
    test_input_backspace_scenario(
        &engine, 
        vec!['n' as i32, 'i' as i32, 'h' as i32, 'a' as i32, 'o' as i32],
        vec![0x08, 0x08, 0x08, 0x08, 0x08]
    )?;
    
    // 测试场景3：输入后选择候选词，然后backspace
    println!("\n4. 测试场景3：输入'wo'，选择候选词，然后backspace");
    test_candidate_selection_backspace(&engine)?;
    
    println!("\n=== 测试完成 ===");
    println!("如果上述测试中preedit能够正确响应backspace删除，说明修复成功。");
    
    Ok(())
}

fn test_input_backspace_scenario(
    engine: &RimeEngine, 
    input_keys: Vec<i32>, 
    backspace_keys: Vec<i32>
) -> Result<(), Box<dyn std::error::Error>> {
    // 清空引擎状态
    engine.clear_composition();
    
    // 输入字符
    println!("  输入字符序列:");
    for &key in &input_keys {
        let processed = engine.process_key(key);
        let preedit = engine.get_preedit();
        let candidates = engine.get_candidates();
        
        println!("    按键: {} -> 处理: {}, preedit: '{}', 候选数: {}", 
                 key as u8 as char, processed, preedit, candidates.len());
        
        if !candidates.is_empty() {
            println!("      前3个候选: {:?}", 
                     candidates.iter().take(3).map(|c| &c.output).collect::<Vec<_>>());
        }
    }
    
    // 测试backspace
    println!("  Backspace删除测试:");
    for &backspace in &backspace_keys {
        let preedit_before = engine.get_preedit();
        let processed = engine.process_key(backspace);
        let preedit_after = engine.get_preedit();
        let candidates = engine.get_candidates();
        
        println!("    BackSpace -> 处理: {}, preedit: '{}' → '{}', 候选数: {}", 
                 processed, preedit_before, preedit_after, candidates.len());
        
        // 检查是否正确删除了字符
        if preedit_before.len() > 0 && preedit_after.len() < preedit_before.len() {
            println!("      ✓ 成功删除字符");
        } else if preedit_before.len() > 0 && preedit_after.is_empty() {
            println!("      ✓ 成功清空组合");
        } else if preedit_before.is_empty() && preedit_after.is_empty() {
            println!("      ○ 组合已空，无需删除");
        } else {
            println!("      ✗ 删除可能失败: 长度没有减少");
        }
    }
    
    Ok(())
}

fn test_candidate_selection_backspace(engine: &RimeEngine) -> Result<(), Box<dyn std::error::Error>> {
    // 清空引擎状态
    engine.clear_composition();
    
    // 输入'wo'
    println!("  输入'wo':");
    for &key in &['w' as i32, 'o' as i32] {
        let processed = engine.process_key(key);
        let preedit = engine.get_preedit();
        let candidates = engine.get_candidates();
        
        println!("    按键: {} -> 处理: {}, preedit: '{}', 候选数: {}", 
                 key as u8 as char, processed, preedit, candidates.len());
    }
    
    let candidates = engine.get_candidates();
    if !candidates.is_empty() {
        println!("  可用候选词: {:?}", 
                 candidates.iter().take(5).map(|c| &c.output).collect::<Vec<_>>());
        
        // 尝试选择第一个候选词（通常是数字键1，对应0x31）
        println!("  尝试选择第一个候选词:");
        let processed = engine.process_key(0x31); // '1'
        let preedit = engine.get_preedit();
        let new_candidates = engine.get_candidates();
        
        println!("    选择结果 -> 处理: {}, preedit: '{}', 候选数: {}", 
                 processed, preedit, new_candidates.len());
        
        // 现在测试backspace
        println!("  选择后测试Backspace:");
        let preedit_before = engine.get_preedit();
        let processed = engine.process_key(0x08); // BackSpace
        let preedit_after = engine.get_preedit();
        let final_candidates = engine.get_candidates();
        
        println!("    BackSpace -> 处理: {}, preedit: '{}' → '{}', 候选数: {}", 
                 processed, preedit_before, preedit_after, final_candidates.len());
        
        if preedit_before != preedit_after {
            println!("      ✓ Backspace在候选选择后仍然有效");
        } else {
            println!("      ○ Backspace可能被组合状态处理");
        }
    } else {
        println!("  没有候选词可供选择");
    }
    
    Ok(())
}