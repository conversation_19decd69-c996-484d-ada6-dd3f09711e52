use std::{cell::Cell, mem::ManuallyDrop};

use log::{error, trace, debug};
use windows::{
    Win32::{
        Foundation::{BOOL, FALSE, RECT, S_OK, E_FAIL},
        UI::TextServices::{
            GUID_PROP_ATTRIBUTE, ITfComposition, ITfCompositionSink, ITfContext,
            ITfContextComposition, ITfEditSession, ITfEditSession_Impl, ITfInsertAtSelection,
            ITfRange, TF_AE_NONE, TF_ANCHOR_END, TF_ANCHOR_START, TF_DEFAULT_SELECTION, TF_ES_READ, TF_ES_READWRITE,
            TF_IAS_QUERYONLY, TF_SELECTION, TF_SELECTIONSTYLE, TF_ST_CORRECTION,
        },
    },
    core::{AsImpl, Interface, Result, VARIANT, implement, HRESULT},
};

// 某些windows-rs版本未导出 TF_IAS_NOQUERY，这里定义常量以保证兼容
const TF_IAS_NOQUERY: u32 = 0x0000;
//----------------------------------------------------------------------------
//
//  Edit of any kind must be operated in edit sessions.
//  It's for safety reasons I guess.
//  But it's a pain in the ass to use such sessions so let's hide them under functions.
//
//----------------------------------------------------------------------------

pub fn start_composition(
    tid: u32,
    context: &ITfContext,
    composition_sink: &ITfCompositionSink,
) -> Result<ITfComposition> {
    trace!("start_composition");
    #[implement(ITfEditSession)]
    struct Session<'a> {
        context: &'a ITfContext,
        composition_sink: &'a ITfCompositionSink,
        composition: Cell<Option<ITfComposition>>, // out
    }

    impl ITfEditSession_Impl for Session<'_> {
        #[allow(non_snake_case)]
        fn DoEditSession(&self, ec: u32) -> Result<()> {
            // to get the current range (namely the selected text or simply the cursor) you insert "nothing"
            // which genius came up with these APIs?
            let range = unsafe {
                self.context
                    .cast::<ITfInsertAtSelection>()?
                    .InsertTextAtSelection(ec, TF_IAS_QUERYONLY, &[])?
            };
            let context_composition = self.context.cast::<ITfContextComposition>()?;
            let composition =
                unsafe { context_composition.StartComposition(ec, &range, self.composition_sink)? };
            self.composition.set(Some(composition));
            Ok(())
        }
    }

    let session = ITfEditSession::from(Session {
        context,
        composition_sink,
        composition: Cell::new(None),
    });

    unsafe {
        let result = context.RequestEditSession(tid, &session, TF_ES_READWRITE)?;
        if result != S_OK {
            Err(result.into())
        } else {
            let session: &Session = session.as_impl();
            Ok(session.composition.take().expect("Composition is None."))
        }
    }
}

pub fn end_composition(tid: u32, context: &ITfContext, composition: &ITfComposition) -> Result<()> {
    trace!("end_composition");
    #[implement(ITfEditSession)]
    struct Session<'a>(&'a ITfComposition);
    impl ITfEditSession_Impl for Session<'_> {
        #[allow(non_snake_case)]
        fn DoEditSession(&self, ec: u32) -> Result<()> {
            unsafe { self.0.EndComposition(ec) }
        }
    }
    let session = ITfEditSession::from(Session(composition));
    unsafe {
        let result = context.RequestEditSession(tid, &session, TF_ES_READWRITE)?;
        if result != S_OK {
            Err(result.into())
        } else {
            Ok(())
        }
    }
}

pub fn set_text(
    tid: u32,
    context: &ITfContext,
    range: ITfRange,
    text: &[u16],
    dispaly_attribute: Option<&VARIANT>,
) -> Result<()> {
    #[implement(ITfEditSession)]
    struct Session<'a> {
        context: &'a ITfContext,
        range: ITfRange,
        text: &'a [u16],
        dispaly_attribute: Option<&'a VARIANT>,
    }

    impl ITfEditSession_Impl for Session<'_> {
        #[allow(non_snake_case)]
        fn DoEditSession(&self, ec: u32) -> Result<()> {
            unsafe {
                // apply underscore
                if let Some(display_attribute) = self.dispaly_attribute {
                    self.range.SetText(ec, TF_ST_CORRECTION, self.text)?;
                    let prop = self.context.GetProperty(&GUID_PROP_ATTRIBUTE)?;
                    if let Err(e) = prop.SetValue(ec, &self.range, display_attribute) {
                        error!("Failed to set display attribute. {}", e);
                    }
                } else {
                    // using 0 for dwflag will remove the propety
                    self.range.SetText(ec, 0, self.text)?;
                }
                if self.text.is_empty() {
                    return Ok(());
                }
                // move the cursor to the end
                self.range.Collapse(ec, TF_ANCHOR_END)?;
                let selection = TF_SELECTION {
                    range: ManuallyDrop::new(Some(self.range.clone())),
                    style: TF_SELECTIONSTYLE {
                        ase: TF_AE_NONE,
                        fInterimChar: FALSE,
                    },
                };
                self.context.SetSelection(ec, &[selection])?;
                Ok(())
            }
        }
    }

    let session = ITfEditSession::from(Session {
        context,
        range,
        text,
        dispaly_attribute,
    });
    unsafe {
        let result = context.RequestEditSession(tid, &session, TF_ES_READWRITE)?;
        if result != S_OK {
            Err(result.into())
        } else {
            Ok(())
        }
    }
}

pub fn insert_text(tid: u32, context: &ITfContext, text: &[u16]) -> Result<()> {
    #[implement(ITfEditSession)]
    struct Session<'a> {
        context: &'a ITfContext,
        text: &'a [u16],
    }

    impl ITfEditSession_Impl for Session<'_> {
        #[allow(non_snake_case)]
        fn DoEditSession(&self, ec: u32) -> Result<()> {
            unsafe {
                let range = self
                    .context
                    .cast::<ITfInsertAtSelection>()?
                    .InsertTextAtSelection(ec, TF_IAS_QUERYONLY, &[])?;
                // insert text via InsertTextAtSelection directly would crash the client
                // what's wrong with these magical APIs
                range.SetText(ec, TF_ST_CORRECTION, self.text)?;
                range.Collapse(ec, TF_ANCHOR_END)?;
                let selection = TF_SELECTION {
                    range: ManuallyDrop::new(Some(range.clone())),
                    style: TF_SELECTIONSTYLE {
                        ase: TF_AE_NONE,
                        fInterimChar: FALSE,
                    },
                };
                self.context.SetSelection(ec, &[selection])
            }
        }
    }

    let session = ITfEditSession::from(Session { context, text });
    unsafe {
        let result = context.RequestEditSession(tid, &session, TF_ES_READWRITE)?;
        if result != S_OK {
            Err(result.into())
        } else {
            Ok(())
        }
    }
}

pub fn get_pos(tid: u32, context: &ITfContext, range: &ITfRange) -> Result<(i32, i32)> {
    #[implement(ITfEditSession)]
    struct Session<'a> {
        context: &'a ITfContext,
        range: &'a ITfRange,
        pos: Cell<(i32, i32)>,
    }

    impl ITfEditSession_Impl for Session<'_> {
        #[allow(non_snake_case)]
        fn DoEditSession(&self, ec: u32) -> Result<()> {
            unsafe {
                let mut rect = RECT::default();
                let mut clipped = BOOL::default();
                let view = self.context.GetActiveView()?;
                view.GetTextExt(ec, self.range, &mut rect, &mut clipped)?;
                self.pos.set((rect.left, rect.bottom));
                Ok(())
            }
        }
    }

    let session = ITfEditSession::from(Session {
        context,
        range,
        pos: Cell::new((0, 0)),
    });
    unsafe {
        let result = context.RequestEditSession(tid, &session, TF_ES_READWRITE)?;
        if result != S_OK {
            Err(result.into())
        } else {
            let session: &Session = session.as_impl();
            Ok(session.pos.take())
        }
    }
}

/// 获取光标前的文本内容（用于AI补全的上下文）
pub fn get_context_before_cursor(
    tid: u32,
    context: &ITfContext,
    max_length: usize,
) -> Result<String> {
    #[implement(ITfEditSession)]
    struct Session {
        context: ITfContext,
        max_length: usize,
        text: Cell<String>,
    }

    impl ITfEditSession_Impl for Session {
        #[allow(non_snake_case)]
        fn DoEditSession(&self, ec: u32) -> Result<()> {
            unsafe {
                let mut success = false;
                
                // 方法1: 尝试通过 GetSelection 获取当前选择
                // 这种方法对大多数应用兼容性最好，包括记事本
                let mut fetched: u32 = 0;
                let selection0 = TF_SELECTION {
                    range: ManuallyDrop::new(None),
                    style: TF_SELECTIONSTYLE { ase: TF_AE_NONE, fInterimChar: FALSE },
                };
                let mut selections: [TF_SELECTION; 1] = [selection0];
                
                if self.context.GetSelection(ec, TF_DEFAULT_SELECTION as u32, &mut selections, &mut fetched).is_ok() && fetched > 0 {
                    if let Some(selection_range) = selections[0].range.take() {
                        // 克隆范围用于获取上下文，避免影响原始选择
                        if let Ok(context_range) = selection_range.Clone() {
                            // 折叠到插入点（选择的开始位置，这样更安全）
                            if context_range.Collapse(ec, TF_ANCHOR_START).is_ok() {
                                let mut moved: i32 = 0;
                                let shift_amount = -(self.max_length as i32);
                                
                                // 尝试向前扩展范围获取上下文
                                if context_range.ShiftStart(ec, shift_amount, &mut moved, std::ptr::null()).is_ok() {
                                    let actual_length = (-moved) as usize;
                                    if actual_length > 0 {
                                        let mut buf = vec![0u16; actual_length + 16]; // 额外缓冲区
                                        let mut len: u32 = 0;
                                        
                                        if context_range.GetText(ec, 0, &mut buf, &mut len).is_ok() && len > 0 {
                                            let text = String::from_utf16_lossy(&buf[..len as usize]);
                                            // 过滤掉控制字符，只保留可打印字符
                                            let cleaned_text: String = text.chars()
                                                .filter(|c| !c.is_control() && *c != '\u{0}' && *c != '\u{FFFF}')
                                                .collect();
                                            if !cleaned_text.trim().is_empty() {
                                                self.text.set(cleaned_text);
                                                success = true;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 方法2: 如果方法1失败，尝试 InsertTextAtSelection
                if !success {
                    if let Ok(insert_service) = self.context.cast::<ITfInsertAtSelection>() {
                        if let Ok(caret_range) = insert_service.InsertTextAtSelection(ec, TF_IAS_QUERYONLY, &[]) {
                            if let Ok(context_range) = caret_range.Clone() {
                                // 确保范围在光标位置
                                if context_range.Collapse(ec, TF_ANCHOR_START).is_ok() {
                                    let mut moved: i32 = 0;
                                    let shift_amount = -(self.max_length as i32);
                                    
                                    if context_range.ShiftStart(ec, shift_amount, &mut moved, std::ptr::null()).is_ok() {
                                        let actual_length = (-moved) as usize;
                                        if actual_length > 0 {
                                            let mut buf = vec![0u16; actual_length + 16];
                                            let mut len: u32 = 0;
                                            
                                            if context_range.GetText(ec, 0, &mut buf, &mut len).is_ok() && len > 0 {
                                                let text = String::from_utf16_lossy(&buf[..len as usize]);
                                                let cleaned_text: String = text.chars()
                                                    .filter(|c| !c.is_control() && *c != '\u{0}' && *c != '\u{FFFF}')
                                                    .collect();
                                                if !cleaned_text.trim().is_empty() {
                                                    self.text.set(cleaned_text);
                                                    success = true;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 如果都失败了，记录调试信息但不报错
                if !success {
                    debug!("Unable to get context text from TSF - this is normal for some applications like Notepad");
                    self.text.set(String::new());
                }
                
                Ok(())
            }
        }
    }

    let session = ITfEditSession::from(Session {
        context: context.clone(),
        max_length,
        text: Cell::new(String::new()),
    });

    unsafe {
        let result = context.RequestEditSession(tid, &session, TF_ES_READ)?;
        if result != S_OK {
            Err(result.into())
        } else {
            let session: &Session = session.as_impl();
            Ok(session.text.take())
        }
    }
}
/// 直接提交AI补全文本到宿主（不使用灰色预览属性，兼容性更好）
pub fn commit_ai_completion_text(
    tid: u32,
    context: &ITfContext,
    completion_text: &str,
) -> Result<()> {
    #[implement(ITfEditSession)]
    struct Session<'a> {
        context: &'a ITfContext,
        completion_text: &'a str,
    }

    impl ITfEditSession_Impl for Session<'_> {
        #[allow(non_snake_case)]
        fn DoEditSession(&self, ec: u32) -> Result<()> {
            unsafe {
                // 在编辑会话中，使用 InsertTextAtSelection 插入文本
                let insert = self.context.cast::<ITfInsertAtSelection>()?;
                let text_utf16: Vec<u16> = self.completion_text.encode_utf16().collect();
                // TF_IAS_NOQUERY: 不查询范围，直接插入；由宿主更新选择范围
                insert.InsertTextAtSelection(ec, windows::Win32::UI::TextServices::INSERT_TEXT_AT_SELECTION_FLAGS(TF_IAS_NOQUERY), &text_utf16)?;
                Ok(())
            }
        }
    }

    let session = ITfEditSession::from(Session { context, completion_text });
    unsafe {
        // 允许 TS_S_ASYNC：异步情况下，系统稍后会调用 DoEditSession 完成插入
        let _hr = context.RequestEditSession(tid, &session, TF_ES_READWRITE)?;
        Ok(())
    }
}


/// 在光标位置显示AI补全文本（灰色显示）
pub fn show_ai_completion_text(
    tid: u32,
    context: &ITfContext,
    completion_text: &str,
    display_attribute: Option<&VARIANT>,
) -> Result<ITfRange> {
    #[implement(ITfEditSession)]
    struct Session<'a> {
        context: &'a ITfContext,
        completion_text: &'a str,
        display_attribute: Option<&'a VARIANT>,
        range: Cell<Option<ITfRange>>,
        error_msg: Cell<String>,
    }

    impl ITfEditSession_Impl for Session<'_> {
        #[allow(non_snake_case)]
        fn DoEditSession(&self, ec: u32) -> Result<()> {
            unsafe {
                // 获取当前光标位置
                let cursor_range = match self.context.cast::<ITfInsertAtSelection>() {
                    Ok(insert_service) => {
                        match insert_service.InsertTextAtSelection(ec, TF_IAS_QUERYONLY, &[]) {
                            Ok(range) => range,
                            Err(e) => {
                                self.error_msg.set(format!("InsertTextAtSelection failed: {:?}", e));
                                return Err(e);
                            }
                        }
                    },
                    Err(e) => {
                        self.error_msg.set(format!("Cast to ITfInsertAtSelection failed: {:?}", e));
                        return Err(e);
                    }
                };

                // 创建一个新的范围来显示补全文本
                let completion_range = match cursor_range.Clone() {
                    Ok(range) => range,
                    Err(e) => {
                        self.error_msg.set(format!("Range Clone failed: {:?}", e));
                        return Err(e);
                    }
                };

                // 将补全文本转换为UTF-16
                let text_utf16: Vec<u16> = self.completion_text.encode_utf16().collect();
                
                if text_utf16.is_empty() {
                    self.error_msg.set("Completion text is empty after UTF-16 conversion".to_string());
                    return Err(E_FAIL.into());
                }

                // 尝试在光标位置插入补全文本（不使用 TF_ST_CORRECTION 避免某些应用的问题）
                if let Err(e) = completion_range.SetText(ec, 0, &text_utf16) {
                    self.error_msg.set(format!("SetText failed: {:?}", e));
                    return Err(e);
                }

                // 应用灰色显示属性（可选）
                if let Some(display_attribute) = self.display_attribute {
                    if let Ok(prop) = self.context.GetProperty(&GUID_PROP_ATTRIBUTE) {
                        if let Err(e) = prop.SetValue(ec, &completion_range, display_attribute) {
                            // 显示属性设置失败不致命，但要记录
                            debug!("Failed to set AI completion display attribute: {:?}", e);
                        }
                    }
                }

                // 将光标移回原位置（不移动光标）
                if let Err(e) = cursor_range.Collapse(ec, TF_ANCHOR_START) {
                    self.error_msg.set(format!("Cursor Collapse failed: {:?}", e));
                    return Err(e);
                }
                
                let selection = TF_SELECTION {
                    range: ManuallyDrop::new(Some(cursor_range)),
                    style: TF_SELECTIONSTYLE {
                        ase: TF_AE_NONE,
                        fInterimChar: FALSE,
                    },
                };
                
                if let Err(e) = self.context.SetSelection(ec, &[selection]) {
                    self.error_msg.set(format!("SetSelection failed: {:?}", e));
                    return Err(e);
                }

                self.range.set(Some(completion_range));
                Ok(())
            }
        }
    }

    let session = ITfEditSession::from(Session {
        context,
        completion_text,
        display_attribute,
        range: Cell::new(None),
        error_msg: Cell::new(String::new()),
    });

    unsafe {
        let result = context.RequestEditSession(tid, &session, TF_ES_READWRITE)?;
        if result != S_OK {
            let session: &Session = session.as_impl();
            let error_msg = session.error_msg.take();
            if !error_msg.is_empty() {
                debug!("AI completion display failed with details: {}", error_msg);
            }
            Err(result.into())
        } else {
            let session: &Session = session.as_impl();
            match session.range.take() {
                Some(range) => Ok(range),
                None => {
                    let error_msg = session.error_msg.take();
                    debug!("AI completion display failed: {}", error_msg);
                    Err(E_FAIL.into())
                }
            }
        }
    }
}

/// 清除AI补全文本显示
pub fn clear_ai_completion_text(
    tid: u32,
    context: &ITfContext,
    completion_range: &ITfRange,
) -> Result<()> {
    #[implement(ITfEditSession)]
    struct Session<'a> {
        context: &'a ITfContext,
        completion_range: &'a ITfRange,
    }

    impl ITfEditSession_Impl for Session<'_> {
        #[allow(non_snake_case)]
        fn DoEditSession(&self, ec: u32) -> Result<()> {
            unsafe {
                // 清除补全文本
                self.completion_range.SetText(ec, 0, &[])?;
                Ok(())
            }
        }
    }

    let session = ITfEditSession::from(Session {
        context,
        completion_range,
    });

    unsafe {
        let result = context.RequestEditSession(tid, &session, TF_ES_READWRITE)?;
        if result != S_OK {
            Err(result.into())
        } else {
            Ok(())
        }
    }
}
