# 系统级上下文获取解决方案

## 问题背景

在之前的实现中，我们发现TSF (Text Services Framework) 在简单应用如记事本中存在兼容性限制，无法可靠地获取光标前的文本上下文。为了解决这个问题，我们设计了多层级的系统级解决方案。

## 解决方案架构

### 三层 Fallback 架构

我们实现了一个智能的混合上下文获取器，按优先级使用三种方法：

```
1. TSF API (优先)
   ↓ (失败时)
2. Windows Accessibility API 
   ↓ (失败时)  
3. 剪贴板方法 (最后手段)
```

## 方案详细说明

### 方案1: TSF API 🎯 **优先选择**

**实现**: `edit_session.rs` 中的 `get_context_before_cursor`

**优点**:
- 系统级集成，性能最佳
- 不干扰用户体验
- 支持富文本应用（如Word）

**缺点**:
- 在简单应用（如记事本）中兼容性有限
- 依赖TSF上下文可用性

**适用场景**: Word、VS Code、现代编辑器

### 方案2: Windows Accessibility API ⚡ **推荐Fallback**

**实现**: `accessibility_context.rs`

**优点**:
- 系统级API，兼容性好
- 专门为辅助功能设计
- 可以获取任何应用的文本内容
- 不会干扰用户剪贴板

**缺点**:
- 需要用户授权辅助功能权限
- 某些应用可能不支持UI Automation

**权限要求**:
```
设置 > 隐私和安全性 > 辅助功能 > 启用输入法的辅助功能权限
```

**适用场景**: 记事本、简单编辑器、部分第三方应用

### 方案3: 剪贴板方法 🔧 **最后手段**

**实现**: `clipboard_context.rs`

**工作原理**:
1. 保存用户的原始剪贴板内容
2. 模拟 Ctrl+A 选择全部文本
3. 模拟 Ctrl+C 复制到剪贴板
4. 读取剪贴板获取文本
5. 恢复原始剪贴板内容

**优点**:
- 兼容性极好，适用于所有应用
- 不需要特殊权限
- 可以获取完整文本内容

**缺点**:
- 侵入性强，会临时覆盖用户剪贴板
- 可能触发应用的"全选"副作用
- 无法精确定位光标位置

**适用场景**: 其他方法都失败时的最后备选

## 混合上下文提供器

### 核心类: `HybridContextProvider`

```rust
pub struct HybridContextProvider {
    accessibility_provider: Arc<Mutex<AccessibilityContextProvider>>,
    clipboard_provider: Arc<Mutex<ClipboardContextProvider>>,
    tsf_enabled: bool,
    accessibility_enabled: bool, 
    clipboard_enabled: bool,
    last_method_used: String,
}
```

### 智能选择逻辑

```rust
pub fn get_context_before_cursor(
    &mut self,
    tid: u32,
    context: Option<&ITfContext>,
    max_length: usize,
) -> Result<String, String>
```

**执行流程**:
1. 如果TSF上下文可用且启用 → 尝试TSF方法
2. 如果失败且Accessibility启用 → 尝试Accessibility API
3. 如果失败且剪贴板启用 → 尝试剪贴板方法
4. 所有方法失败 → 返回空字符串（允许AI补全继续工作）

## 配置和使用

### 1. 默认配置

```rust
let provider = HybridContextProvider::new();
// TSF: 启用
// Accessibility: 自动检测并启用
// 剪贴板: 默认禁用（避免干扰用户）
```

### 2. 启用剪贴板方法

```rust
provider.enable_clipboard(); // 仅在其他方法都失败时使用
```

### 3. 检查可用性

```rust
let availability = provider.check_availability();
println!("TSF: {}", availability.tsf_available);
println!("Accessibility: {}", availability.accessibility_available);
println!("Ready: {}", availability.hybrid_ready);
```

### 4. 调试信息

```rust
println!("{}", provider.get_diagnostics());
println!("Last method used: {}", provider.get_last_method_used());
```

## 测试程序

### 1. 混合上下文测试

```bash
cargo run --bin test_hybrid_context
```

**功能**:
- 实时测试所有方法
- 方法切换测试
- 权限检查
- 诊断信息显示

### 2. AI补全修复测试

```bash
cargo run --bin test_ai_fixes
```

**功能**:
- 验证AI补全API连接
- 测试配置加载
- 错误处理验证

## 集成到TSF

### 修改点

1. **`src/tsf/mod.rs`**: 
   - 添加 `HybridContextProvider` 字段
   - 修改 `trigger_ai_completion` 使用混合提供器

2. **导入新模块**:
   ```rust
   use crate::hybrid_context::HybridContextProvider;
   ```

3. **初始化**:
   ```rust
   context_provider: HybridContextProvider::new(),
   ```

4. **使用**:
   ```rust
   match self.context_provider.get_context_before_cursor(
       self.tid,
       self.context.as_ref(),
       ai_config.max_context_length,
   ) {
       Ok(context_text) => { /* 处理成功 */ }
       Err(e) => { /* 处理失败 */ }
   }
   ```

## 性能考虑

### 方法优先级设计

1. **TSF** (最快): ~1ms
2. **Accessibility** (中等): ~10-50ms  
3. **剪贴板** (最慢): ~100-200ms

### 缓存策略

- 短期内重复请求可以考虑缓存结果
- 避免频繁的Accessibility API调用
- 剪贴板方法有内置延迟以确保操作完成

## 用户体验优化

### 1. 渐进式启用

```
开始: 仅TSF + Accessibility (自动)
↓
如果用户遇到问题: 提示启用剪贴板方法
↓  
高级用户: 完全控制各方法的启用/禁用
```

### 2. 错误处理

- 所有错误都降级为调试信息，不中断AI补全流程
- 提供详细的诊断信息帮助用户排查问题
- 自动fallback确保最大兼容性

### 3. 权限指导

- 检测Accessibility权限状态
- 提供清晰的权限启用指导
- 权限问题不会阻止其他方法工作

## 未来改进

### 1. 智能应用检测

- 根据当前应用智能选择最佳方法
- 建立应用兼容性数据库
- 学习用户使用模式

### 2. 性能优化

- 方法选择的机器学习
- 预测性上下文获取
- 更智能的缓存策略

### 3. 扩展性

- 插件化的上下文提供器架构
- 支持第三方上下文获取方法
- 应用特定的优化策略

## 总结

通过这个三层fallback架构，我们解决了TSF在简单应用中的兼容性问题：

✅ **记事本**: Accessibility API 或剪贴板方法  
✅ **Word**: TSF API（最佳体验）  
✅ **VS Code**: TSF API 或 Accessibility API  
✅ **其他应用**: 自动选择最佳方法  

这个解决方案确保了AI补全功能在各种Windows应用中都能可靠工作，同时保持了最佳的用户体验。