use std::thread;
use std::time::Duration;
use log::debug;
use windows::{
    Win32::{
        Foundation::{HWND, HANDLE, HGLOBAL},
        System::{
            DataExchange::{
                GetClipboardData, SetClipboardData, EmptyClipboard,
                OpenClipboard, CloseClipboard,
            },
            Memory::{GlobalAlloc, GlobalLock, GlobalUnlock, GMEM_MOVEABLE},
        },
        UI::{
            Input::KeyboardAndMouse::{
                SendInput, INPUT, INPUT_KEYBOARD, KEYBDINPUT, KEYEVENTF_KEYUP,
                VK_CONTROL, VK_A, VK_C,
            },
            WindowsAndMessaging::{GetForegroundWindow},
        },
    },
    core::{PCWSTR},
};

// 剪贴板格式常量
const CF_UNICODETEXT: u32 = 13;

/// 基于剪贴板的上下文获取器
/// 通过模拟Ctrl+A、Ctrl+C获取文本，然后恢复原始剪贴板内容
/// 这是一个invasive方案，但兼容性极好
pub struct ClipboardContextProvider {
    original_clipboard: Option<String>,
    last_error: Option<String>,
}

impl ClipboardContextProvider {
    pub fn new() -> Self {
        Self {
            original_clipboard: None,
            last_error: None,
        }
    }

    /// 获取当前活动窗口的所有文本内容
    /// 注意：这会临时覆盖用户的剪贴板内容
    pub fn get_full_text_content(&mut self) -> Result<String, String> {
        debug!("Starting clipboard-based text extraction");

        // 步骤1: 保存原始剪贴板内容
        if let Err(e) = self.save_original_clipboard() {
            self.last_error = Some(e.clone());
            return Err(e);
        }

        // 步骤2: 清空剪贴板
        if let Err(e) = self.clear_clipboard() {
            self.last_error = Some(e.clone());
            return Err(e);
        }

        // 步骤3: 模拟Ctrl+A选择全部
        if let Err(e) = self.simulate_select_all() {
            self.last_error = Some(e.clone());
            self.restore_original_clipboard().ok(); // 尝试恢复
            return Err(e);
        }

        // 等待选择完成
        thread::sleep(Duration::from_millis(50));

        // 步骤4: 模拟Ctrl+C复制
        if let Err(e) = self.simulate_copy() {
            self.last_error = Some(e.clone());
            self.restore_original_clipboard().ok(); // 尝试恢复
            return Err(e);
        }

        // 等待复制完成
        thread::sleep(Duration::from_millis(100));

        // 步骤5: 读取剪贴板内容
        let result = match self.read_clipboard() {
            Ok(text) => {
                debug!("Successfully extracted {} characters from clipboard", text.len());
                Ok(text)
            }
            Err(e) => {
                self.last_error = Some(e.clone());
                Err(e)
            }
        };

        // 步骤6: 恢复原始剪贴板内容
        self.restore_original_clipboard().ok();

        result
    }

    /// 获取光标前的上下文（基于全文本的估算）
    pub fn get_context_before_cursor(&mut self, max_length: usize) -> Result<String, String> {
        let full_text = self.get_full_text_content()?;
        
        if full_text.is_empty() {
            return Ok(String::new());
        }

        // 简单策略：返回文本的最后max_length个字符
        // 这不是精确的光标位置，但对于简单应用是合理的估算
        if full_text.len() <= max_length {
            Ok(full_text)
        } else {
            let truncated: String = full_text.chars()
                .rev()
                .take(max_length)
                .collect::<String>()
                .chars()
                .rev()
                .collect();
            Ok(truncated)
        }
    }

    /// 保存原始剪贴板内容
    fn save_original_clipboard(&mut self) -> Result<(), String> {
        self.original_clipboard = match self.read_clipboard() {
            Ok(content) => Some(content),
            Err(_) => {
                // 如果读取失败，假设剪贴板为空
                debug!("Original clipboard appears to be empty or inaccessible");
                Some(String::new())
            }
        };
        Ok(())
    }

    /// 清空剪贴板
    fn clear_clipboard(&self) -> Result<(), String> {
        unsafe {
            let hwnd = GetForegroundWindow();
            if OpenClipboard(hwnd).is_err() {
                return Err("Failed to open clipboard for clearing".to_string());
            }

            if EmptyClipboard().is_err() {
                CloseClipboard().ok();
                return Err("Failed to empty clipboard".to_string());
            }

            if CloseClipboard().is_err() {
                return Err("Failed to close clipboard after clearing".to_string());
            }
        }
        Ok(())
    }

    /// 恢复原始剪贴板内容
    fn restore_original_clipboard(&mut self) -> Result<(), String> {
        if let Some(ref original) = self.original_clipboard.take() {
            self.write_clipboard(original)
        } else {
            Ok(())
        }
    }

    /// 读取剪贴板内容
    fn read_clipboard(&self) -> Result<String, String> {
        unsafe {
            let hwnd = GetForegroundWindow();
            if OpenClipboard(hwnd).is_err() {
                return Err("Failed to open clipboard for reading".to_string());
            }

            let result = match GetClipboardData(CF_UNICODETEXT) {
                Ok(handle) => {
                    if handle.0 == 0 {
                        Ok(String::new())
                    } else {
                        // 使用HGLOBAL来进行内存操作
                        let hglobal = HGLOBAL(handle.0 as *mut std::ffi::c_void);
                        let ptr = GlobalLock(hglobal);
                        if ptr.is_null() {
                            return Err("Failed to lock clipboard memory".to_string());
                        }
                        let text = PCWSTR(ptr as *const u16).to_string()
                            .map_err(|e| format!("Failed to convert clipboard text: {:?}", e))?;
                        GlobalUnlock(hglobal).ok();
                        Ok(text)
                    }
                }
                Err(e) => Err(format!("Failed to get clipboard data: {:?}", e))
            };

            CloseClipboard().ok();
            result
        }
    }

    /// 写入剪贴板内容
    fn write_clipboard(&self, text: &str) -> Result<(), String> {
        if text.is_empty() {
            return self.clear_clipboard();
        }

        unsafe {
            let hwnd = GetForegroundWindow();
            if OpenClipboard(hwnd).is_err() {
                return Err("Failed to open clipboard for writing".to_string());
            }

            if EmptyClipboard().is_err() {
                CloseClipboard().ok();
                return Err("Failed to empty clipboard before writing".to_string());
            }

            // 转换为UTF-16
            let wide_text: Vec<u16> = text.encode_utf16().chain(std::iter::once(0)).collect();
            let byte_size = wide_text.len() * 2;

            let h_mem = match GlobalAlloc(GMEM_MOVEABLE, byte_size) {
                Ok(handle) => handle,
                Err(e) => {
                    CloseClipboard().ok();
                    return Err(format!("Failed to allocate clipboard memory: {:?}", e));
                }
            };

            let ptr = GlobalLock(h_mem);
            if ptr.is_null() {
                CloseClipboard().ok();
                return Err("Failed to lock clipboard memory for writing".to_string());
            }
            
            std::ptr::copy_nonoverlapping(
                wide_text.as_ptr(),
                ptr as *mut u16,
                wide_text.len(),
            );
            GlobalUnlock(h_mem).ok();

            if SetClipboardData(CF_UNICODETEXT, HANDLE(h_mem.0 as isize)).is_err() {
                CloseClipboard().ok();
                return Err("Failed to set clipboard data".to_string());
            }

            if CloseClipboard().is_err() {
                return Err("Failed to close clipboard after writing".to_string());
            }
        }
        Ok(())
    }

    /// 模拟Ctrl+A（全选）
    fn simulate_select_all(&self) -> Result<(), String> {
        self.send_key_combination(&[VK_CONTROL], VK_A)
    }

    /// 模拟Ctrl+C（复制）
    fn simulate_copy(&self) -> Result<(), String> {
        self.send_key_combination(&[VK_CONTROL], VK_C)
    }

    /// 发送组合键
    fn send_key_combination(&self, modifiers: &[windows::Win32::UI::Input::KeyboardAndMouse::VIRTUAL_KEY], key: windows::Win32::UI::Input::KeyboardAndMouse::VIRTUAL_KEY) -> Result<(), String> {
        unsafe {
            let mut inputs = Vec::new();

            // 按下修饰键
            for &modifier in modifiers {
                inputs.push(INPUT {
                    r#type: INPUT_KEYBOARD,
                    Anonymous: windows::Win32::UI::Input::KeyboardAndMouse::INPUT_0 {
                        ki: KEYBDINPUT {
                            wVk: modifier,
                            wScan: 0,
                            dwFlags: windows::Win32::UI::Input::KeyboardAndMouse::KEYBD_EVENT_FLAGS(0),
                            time: 0,
                            dwExtraInfo: 0,
                        },
                    },
                });
            }

            // 按下主键
            inputs.push(INPUT {
                r#type: INPUT_KEYBOARD,
                Anonymous: windows::Win32::UI::Input::KeyboardAndMouse::INPUT_0 {
                    ki: KEYBDINPUT {
                        wVk: key,
                        wScan: 0,
                        dwFlags: windows::Win32::UI::Input::KeyboardAndMouse::KEYBD_EVENT_FLAGS(0),
                        time: 0,
                        dwExtraInfo: 0,
                    },
                },
            });

            // 释放主键
            inputs.push(INPUT {
                r#type: INPUT_KEYBOARD,
                Anonymous: windows::Win32::UI::Input::KeyboardAndMouse::INPUT_0 {
                    ki: KEYBDINPUT {
                        wVk: key,
                        wScan: 0,
                        dwFlags: KEYEVENTF_KEYUP,
                        time: 0,
                        dwExtraInfo: 0,
                    },
                },
            });

            // 释放修饰键
            for &modifier in modifiers.iter().rev() {
                inputs.push(INPUT {
                    r#type: INPUT_KEYBOARD,
                    Anonymous: windows::Win32::UI::Input::KeyboardAndMouse::INPUT_0 {
                        ki: KEYBDINPUT {
                            wVk: modifier,
                            wScan: 0,
                            dwFlags: KEYEVENTF_KEYUP,
                            time: 0,
                            dwExtraInfo: 0,
                        },
                    },
                });
            }

            let sent = SendInput(&inputs, std::mem::size_of::<INPUT>() as i32);
            if sent != inputs.len() as u32 {
                return Err(format!("Failed to send all key inputs: sent {}/{}", sent, inputs.len()));
            }
        }
        Ok(())
    }

    /// 获取最后的错误信息
    pub fn get_last_error(&self) -> Option<&str> {
        self.last_error.as_deref()
    }
}

impl Default for ClipboardContextProvider {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_clipboard_provider_creation() {
        let provider = ClipboardContextProvider::new();
        assert!(provider.original_clipboard.is_none());
        assert!(provider.last_error.is_none());
    }
}