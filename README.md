# Chinese IME

A Chinese Input Method Editor (IME) built with Rust and the Windows Text Services Framework (TSF). This IME provides Pinyin-based Chinese character input with candidate selection.

![](./doc/preview.gif)

## Install

Build from source (see Build from Source section below) or download a pre-built installer when available.

## Setup

To use the Chinese IME:

1. Press <kbd>Win</kbd> + <kbd>Space</kbd> to switch to the input method.
2. Use any font that supports Chinese characters (most modern fonts work fine).

The IME supports standard Chinese character display and input.

## Use

To input Chinese characters:

1. Type Pinyin (romanized Chinese) for the characters you want
2. Press <kbd>Space</kbd> to select the first candidate, or press <kbd>1</kbd> ~ <kbd>5</kbd> to select a specific candidate
3. Press <kbd>Enter</kbd> to input the raw Pinyin text instead

The candidate list shows Chinese character options based on your Pinyin input. Use the number keys or arrow keys to navigate and select characters.

Basic punctuation is supported:
- Standard punctuation marks work as expected
- Chinese punctuation is automatically handled


## Customize

Dictionary files are stored in `%APPDATA%/ChineseIME/dict`. The format follows these rules:

1. Entries are written as `{pinyin} {character 1} {character 2} ... {character n}`
2. Comments start with `#`

Here's a minimal example:

```
ni     你 尼 泥
hao    好 号 豪
ma     吗 妈 马
```

## Configure

Configure the appearance and behavior of the input method by editing `%APPDATA%/ChineseIME/conf.toml`. Here's the default configuration:

```Toml
[font]
name = "Microsoft YaHei"
size = 20

[layout]
vertical = false

[color]
clip = "#0078D7"
background = "#FAFAFA"
highlight = "#E8E8FF"
index = "#A0A0A0"
candidate = "black"
highlighted = "black"

[behavior]
toggle = "Ctrl"
cjk_space = true
```

## Build from Source

Prerequisites:

1. Rust (with the MSVC toolchain)
2. Git Bash (for the `just` build tool)
3. Inno Setup (only for creating installers)

Run the following commands to setup the development environment:

```
cargo install just
just setup
```

To build the project and register the newly built IME for testing, run:

```
just build
```

When you're done testing, you can unregister the IME with:

```
just unreg
```

Create an installer for the project by running:

```
just pack
```

## Architecture

This Chinese IME is built using:
- **Rust** for the core implementation
- **Windows Text Services Framework (TSF)** for system integration
- **COM interfaces** for Windows compatibility
- **Dictionary-based input** for Pinyin to Chinese character conversion

The project structure:
- `src/engine/` - Input processing and dictionary management
- `src/tsf/` - TSF interface implementations
- `src/ui/` - Candidate list and user interface
- `res/dict/` - Chinese character dictionaries

## Notes

- Press <kbd>Ctrl</kbd> to toggle the input method on/off
- The IME registers with Windows as a standard input method
- Dictionary files can be customized in `%APPDATA%/ChineseIME/dict/`