# AI文本自动补全功能

本文档介绍如何在Rust输入法项目中使用AI文本自动补全功能。

## 功能概述

AI文本自动补全功能可以根据用户输入的上下文，智能预测并建议接下来可能的文本内容，提升输入效率和用户体验。

### 核心特性

- **智能文本续写**：基于AI大语言模型的文本自动续写/补全
- **上下文感知**：获取光标前的文本内容作为上下文
- **OpenAI兼容API**：支持OpenAI兼容的API接口，包括Ollama本地服务
- **灰色预览显示**：补全文本以灰色显示，与已输入文本在视觉上区分
- **Tab键接受**：用户按Tab键接受补全，光标自动移动到补全文本末尾
- **多种触发方式**：支持左Ctrl键触发和2秒空闲自动触发
- **加载指示器**：API调用期间显示旋转符号等视觉反馈

## 配置说明

在 `%APPDATA%/ChineseIME/conf.toml` 文件中添加以下配置：

```toml
[ai_completion]
# 是否启用AI文本补全功能
enabled = false
# API端点（支持OpenAI兼容接口，如Ollama）
api_endpoint = "http://localhost:11434/v1/chat/completions"
# API密钥（可选，本地Ollama不需要）
api_key = ""
# 使用的模型名称
model = "qwen2.5:7b"
# 最大生成token数
max_tokens = 100
# 生成温度（0.0-1.0）
temperature = 0.7
# API调用超时时间（秒）
timeout_seconds = 10
# 空闲触发延迟（毫秒）
trigger_delay_ms = 2000
# 最大上下文长度（字符数）
max_context_length = 500
# 是否启用Ctrl键触发
trigger_on_ctrl = true
# 是否启用空闲触发
trigger_on_idle = true
```

### 配置参数说明

- `enabled`: 是否启用AI补全功能
- `api_endpoint`: AI服务的API端点，支持OpenAI兼容接口
- `api_key`: API密钥，使用本地Ollama时可以留空
- `model`: 使用的AI模型名称
- `max_tokens`: 单次补全的最大token数量
- `temperature`: 生成文本的随机性，0.0最确定，1.0最随机
- `timeout_seconds`: API请求超时时间
- `trigger_delay_ms`: 用户停止输入后多久触发自动补全
- `max_context_length`: 获取的上下文最大长度
- `trigger_on_ctrl`: 是否启用Ctrl键手动触发
- `trigger_on_idle`: 是否启用空闲自动触发

## 使用方法

### 1. 启用功能

1. 确保配置文件中 `enabled = true`
2. 配置正确的API端点和模型
3. 重启输入法或重新加载配置

### 2. 触发补全

**手动触发**：
- 在非composition状态下按左Ctrl键

**自动触发**：
- 停止输入超过配置的延迟时间（默认2秒）

### 3. 接受补全

- 按Tab键接受显示的补全文本
- 光标会自动移动到补全文本的末尾

### 4. 取消补全

- 开始新的输入会自动清除补全显示
- 按其他键（如方向键）也会清除补全

## 本地AI服务设置

### 使用Ollama

1. 安装Ollama：访问 https://ollama.ai/ 下载安装
2. 下载模型：`ollama pull qwen2.5:7b`
3. 启动服务：`ollama serve`
4. 配置API端点为：`http://localhost:11434/v1/chat/completions`

### 使用其他OpenAI兼容服务

配置相应的API端点和密钥即可，例如：
- OpenAI官方API
- Azure OpenAI
- 其他兼容OpenAI API格式的服务

## 测试功能

运行测试程序验证AI补全功能：

```bash
cargo run --bin test_ai_completion
```

## 故障排除

### 常见问题

1. **补全不工作**
   - 检查配置文件中 `enabled = true`
   - 确认API服务正在运行
   - 检查网络连接

2. **API调用失败**
   - 验证API端点URL正确
   - 检查API密钥（如果需要）
   - 确认模型名称正确

3. **补全速度慢**
   - 调整 `timeout_seconds` 参数
   - 减少 `max_tokens` 数量
   - 使用更快的AI模型

4. **补全质量不佳**
   - 调整 `temperature` 参数
   - 增加 `max_context_length` 获取更多上下文
   - 尝试不同的AI模型

### 日志调试

查看日志文件了解详细错误信息：
- 位置：`%LOCALAPPDATA%/ChineseIME/log.txt`
- 包含AI补全相关的调试信息和错误消息

## 技术实现

### 架构概述

1. **配置管理**：扩展现有配置系统支持AI补全参数
2. **API客户端**：实现OpenAI兼容的HTTP客户端
3. **上下文获取**：使用TSF API获取光标前的文本
4. **异步处理**：后台线程处理AI请求，避免阻塞用户输入
5. **UI集成**：在光标位置显示灰色补全文本

### 关键组件

- `AiCompletionManager`: 管理AI补全的生命周期
- `AiCompletionClient`: 处理与AI服务的通信
- `edit_session`: TSF会话管理，处理文本显示和编辑
- `key_event_sink`: 键盘事件处理，实现触发和接受逻辑

## 性能考虑

- 异步API调用不会阻塞用户输入
- 智能缓存减少重复请求
- 可配置的超时和延迟参数
- 轻量级的UI更新机制

## 安全性

- 支持本地AI服务，数据不离开本地
- 可配置的上下文长度限制
- 安全的API密钥管理
- 错误处理和异常恢复机制
