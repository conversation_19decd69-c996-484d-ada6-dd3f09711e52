use std::time::Duration;
use log::{debug, error, warn};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::json;
use tokio::time::timeout;

use crate::conf::AiCompletion;

/// AI补全API客户端
#[derive(Clone)]
pub struct AiCompletionClient {
    client: Client,
    config: AiCompletion,
}

/// OpenAI兼容的API请求结构
#[derive(Serialize)]
struct CompletionRequest {
    model: String,
    messages: Vec<Message>,
    max_tokens: u32,
    temperature: f32,
    stream: bool,
}

#[derive(Serialize)]
struct Message {
    role: String,
    content: String,
}

/// API响应结构
#[derive(Deserialize)]
struct CompletionResponse {
    choices: Vec<Choice>,
}

#[derive(Deserialize)]
struct Choice {
    message: MessageResponse,
}

#[derive(Deserialize)]
struct MessageResponse {
    content: String,
}

/// AI补全错误类型
#[derive(Debug)]
pub enum AiCompletionError {
    NetworkError(reqwest::Error),
    TimeoutError,
    ApiError(String),
    ParseError(String),
}

impl From<reqwest::Error> for AiCompletionError {
    fn from(err: reqwest::Error) -> Self {
        AiCompletionError::NetworkError(err)
    }
}

impl AiCompletionClient {
    /// 创建新的AI补全客户端
    pub fn new(config: AiCompletion) -> Result<Self, AiCompletionError> {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .build()
            .map_err(AiCompletionError::NetworkError)?;

        Ok(Self { client, config })
    }

    /// 获取文本补全
    pub async fn get_completion(&self, context: &str) -> Result<String, AiCompletionError> {
        debug!("Requesting AI completion for context: {}", context);

        // 构建请求
        let request = CompletionRequest {
            model: self.config.model.clone(),
            messages: vec![
                Message {
                    role: "system".to_string(),
                    content: "你是一个智能文本补全助手。根据用户提供的上下文，续写接下来可能的文本内容。请保持自然流畅，不要重复已有内容。只返回补全的文本，不要添加解释或其他内容。".to_string(),
                },
                Message {
                    role: "user".to_string(),
                    content: format!("请为以下文本提供自然的续写：{}", context),
                },
            ],
            max_tokens: self.config.max_tokens,
            temperature: self.config.temperature,
            stream: false,
        };

        // 构建HTTP请求
        let mut req_builder = self.client
            .post(&self.config.api_endpoint)
            .header("Content-Type", "application/json")
            .json(&request);

        // 如果有API密钥，添加认证头
        if let Some(ref api_key) = self.config.api_key {
            if !api_key.is_empty() {
                req_builder = req_builder.header("Authorization", format!("Bearer {}", api_key));
            }
        }

        // 发送请求并处理超时
        let response = timeout(
            Duration::from_secs(self.config.timeout_seconds),
            req_builder.send()
        ).await
        .map_err(|_| AiCompletionError::TimeoutError)?
        .map_err(AiCompletionError::NetworkError)?;

        // 检查响应状态
        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            error!("API request failed with status {}: {}", status, error_text);
            return Err(AiCompletionError::ApiError(format!("HTTP {}: {}", status, error_text)));
        }

        // 解析响应
        let response_text = response.text().await
            .map_err(AiCompletionError::NetworkError)?;

        debug!("API response: {}", response_text);

        let completion_response: CompletionResponse = serde_json::from_str(&response_text)
            .map_err(|e| AiCompletionError::ParseError(format!("Failed to parse response: {}", e)))?;

        // 提取补全文本
        if let Some(choice) = completion_response.choices.first() {
            let completion = choice.message.content.trim().to_string();
            debug!("AI completion result: {}", completion);
            Ok(completion)
        } else {
            warn!("No completion choices in API response");
            Err(AiCompletionError::ApiError("No completion choices in response".to_string()))
        }
    }

    /// 检查配置是否有效
    pub fn is_valid_config(&self) -> bool {
        !self.config.api_endpoint.is_empty() && !self.config.model.is_empty()
    }
}

/// AI补全管理器
#[derive(Clone)]
pub struct AiCompletionManager {
    client: Option<AiCompletionClient>,
    enabled: bool,
}

impl AiCompletionManager {
    /// 创建新的AI补全管理器
    pub fn new(config: Option<AiCompletion>) -> Self {
        let (client, enabled) = if let Some(config) = config {
            if config.enabled {
                match AiCompletionClient::new(config) {
                    Ok(client) => {
                        if client.is_valid_config() {
                            debug!("AI completion client initialized successfully");
                            (Some(client), true)
                        } else {
                            warn!("Invalid AI completion configuration");
                            (None, false)
                        }
                    }
                    Err(e) => {
                        error!("Failed to create AI completion client: {:?}", e);
                        (None, false)
                    }
                }
            } else {
                debug!("AI completion is disabled in configuration");
                (None, false)
            }
        } else {
            debug!("No AI completion configuration found");
            (None, false)
        };

        Self { client, enabled }
    }

    /// 检查是否启用AI补全
    pub fn is_enabled(&self) -> bool {
        self.enabled && self.client.is_some()
    }

    /// 获取文本补全（异步）
    pub async fn get_completion(&self, context: &str) -> Option<String> {
        if let Some(ref client) = self.client {
            match client.get_completion(context).await {
                Ok(completion) => Some(completion),
                Err(e) => {
                    error!("AI completion request failed: {:?}", e);
                    None
                }
            }
        } else {
            None
        }
    }
}
