use std::sync::Once;
use log::{info, warn, error};
use crate::Result;
use super::Suggestion;
use rime_api::{
    create_session, full_deploy_and_wait, initialize, setup,
    DeployResult, Traits, Session,
};

/// RIME引擎
///
/// 直接使用 RIME 的 process_key 作为核心处理方式
pub struct RimeEngine {
    /// RIME会话
    pub session: Option<Session>,
}

/// 全局RIME初始化状态
static RIME_INIT: Once = Once::new();
static mut RIME_INITIALIZED: bool = false;

impl RimeEngine {
    /// 创建新的RIME引擎实例
    pub fn new() -> Result<Self> {
        let mut engine = Self {
            session: None,
        };

        engine.initialize()?;
        Ok(engine)
    }

    /// 初始化RIME引擎
    fn initialize(&mut self) -> Result<()> {
        info!("Initializing RIME engine...");

        // 确保RIME全局只初始化一次
        RIME_INIT.call_once(|| {
            let mut traits = Traits::new();

            // 使用默认路径
            if let Ok(appdata) = std::env::var("APPDATA") {
                let rime_dir = format!("{}\\ChineseIME\\rime", appdata);
                traits.set_user_data_dir(&rime_dir);
            }

            traits.set_distribution_name("ChineseIME");
            traits.set_distribution_code_name("ChineseIME");
            traits.set_distribution_version("0.1.0");

            setup(&mut traits);
            initialize(&mut traits);

            // 部署RIME
            match full_deploy_and_wait() {
                DeployResult::Success => {
                    info!("RIME deployment successful");
                    unsafe { RIME_INITIALIZED = true; }
                }
                DeployResult::Failure => {
                    error!("RIME deployment failed");
                }
            }
        });

        // 检查RIME是否初始化成功
        if unsafe { !RIME_INITIALIZED } {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                "RIME initialization failed"
            ).into());
        }

        // 创建RIME会话
        match create_session() {
            Ok(session) => {
                // 选择简体拼音输入方案
                if let Err(e) = session.select_schema("pinyin_simp") {
                    warn!("Failed to select pinyin_simp schema: {:?}, trying luna_pinyin", e);
                    // 如果pinyin_simp不可用，尝试其他方案
                    let _ = session.select_schema("luna_pinyin");
                } else {
                    info!("Successfully selected pinyin_simp schema");
                }

                self.session = Some(session);
                info!("RIME engine initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("Failed to create RIME session: {:?}", e);
                Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Failed to create RIME session: {:?}", e)
                ).into())
            }
        }
    }
    
    /// 核心方法：处理按键输入
    /// 这是 RIME 输入法的核心处理方式
    pub fn process_key(&self, keycode: i32) -> bool {
        if let Some(session) = &self.session {
            use rime_api::{KeyEvent, KeyStatus};
            use librime_sys::{RimeKeyCode, RimeModifier};

            // 检查 RIME 状态
            let has_composition_before = if let Ok(status) = session.status() {
                info!("RIME Status - disabled: {}, composing: {}, ascii_mode: {}, schema: {}",
                      status.is_disabled, status.is_composing, status.is_ascii_mode, status.schema_name());

                // 如果处于 ASCII 模式，尝试切换到中文模式
                if status.is_ascii_mode {
                    info!("RIME is in ASCII mode, trying to switch to Chinese mode");
                    // 使用 Ctrl+Space 或其他方式切换模式
                    let toggle_key = KeyEvent::new(0x20 as RimeKeyCode, 0x04 as RimeModifier); // Ctrl+Space
                    let _ = session.process_key(toggle_key);
                }
                
                status.is_composing
            } else {
                false
            };

            // 创建 KeyEvent
            let key_event = KeyEvent::new(keycode as RimeKeyCode, 0 as RimeModifier);
            let status = session.process_key(key_event);

            let processed = matches!(status, KeyStatus::Accept);
            info!("Processed key: keycode={} ({}), result={:?}", keycode, 
                  if keycode == 0x08 { "BackSpace" } else { &format!("char({})", keycode as u8 as char) }, status);

            // 特殊处理：在组合状态下，即使RIME返回Pass，也要检查是否有组合或候选词
            // 这对于backspace等按键尤其重要
            if !processed {
                let has_composition_after = !self.get_preedit().is_empty();
                let has_candidates = !self.get_candidates().is_empty();
                
                // 如果按键前有组合，或者按键后状态发生了变化，就认为处理了
                if has_composition_before || has_composition_after || has_candidates {
                    info!("RIME returned Pass but composition state changed (before: {}, after: {}, candidates: {}), treating as processed",
                          has_composition_before, has_composition_after, has_candidates);
                    return true;
                }
            }

            processed
        } else {
            error!("RIME session not available for process_key");
            false
        }
    }

    /// 获取当前候选词列表
    pub fn get_candidates(&self) -> Vec<Suggestion> {
        if let Some(session) = &self.session {
            if let Some(context) = session.context() {
                let menu = context.menu();
                let mut suggestions = Vec::new();

                info!("RIME menu has {} candidates", menu.num_candidates);

                for i in 0..menu.num_candidates.min(20) {
                    if let Some(candidate) = menu.candidates.get(i) {
                        let output = candidate.text.to_string();
                        if !output.is_empty() {
                            suggestions.push(Suggestion {
                                output,
                                // 仅作为候选展示，不做分组逻辑
                                groupping: Vec::new(),
                            });
                        }
                    }
                }

                info!("Generated {} suggestions from RIME", suggestions.len());
                suggestions
            } else {
                Vec::new()
            }
        } else {
            Vec::new()
        }
    }

    /// 获取当前组合字符串（预编辑文本）
    pub fn get_preedit(&self) -> String {
        if let Some(session) = &self.session {
            if let Some(context) = session.context() {
                let composition = context.composition();
                // 仅用于展示，不在提交时拼接上屏
                composition.preedit.unwrap_or("").to_string()
            } else {
                String::new()
            }
        } else {
            String::new()
        }
    }

    /// 提交当前输入
    pub fn commit(&self) -> Option<String> {
        if let Some(session) = &self.session {
            if let Some(commit) = session.commit() {
                Some(commit.text().to_string())
            } else {
                None
            }
        } else {
            None
        }
    }

    /// 选择候选词
    pub fn select_candidate(&self, index: usize) -> bool {
        if let Some(session) = &self.session {
            // 使用按键模拟来选择候选词
            let key = format!("{}", index + 1);
            match session.simulate_key_sequence(&key) {
                Ok(_) => {
                    info!("Selected candidate {}", index);
                    true
                }
                Err(e) => {
                    error!("Failed to select candidate {}: {:?}", index, e);
                    false
                }
            }
        } else {
            false
        }
    }

    /// 翻页
    pub fn change_page(&self, backward: bool) -> bool {
        if let Some(session) = &self.session {
            // 使用按键模拟来实现翻页
            let key = if backward { "Page_Up" } else { "Page_Down" };
            match session.simulate_key_sequence(key) {
                Ok(_) => {
                    info!("RIME engine page changed: backward={}", backward);
                    true
                }
                Err(e) => {
                    error!("Failed to simulate page key '{}': {:?}", key, e);
                    false
                }
            }
        } else {
            false
        }
    }

    /// 清空组合
    pub fn clear_composition(&self) {
        if let Some(session) = &self.session {
            let _ = session.commit(); // 提交现有内容
            //let _ = session.simulate_key_sequence("Escape"); // 清空状态
            info!("RIME composition cleared");
        }
    }
    
    /// 检查引擎是否已初始化
    pub fn is_initialized(&self) -> bool {
        self.session.is_some()
    }


}



impl Drop for RimeEngine {
    fn drop(&mut self) {
        if let Some(mut session) = self.session.take() {
            let _ = session.close();
            info!("RIME session closed");
        }
    }
}


