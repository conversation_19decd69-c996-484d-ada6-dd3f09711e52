mod composition;
pub mod display_attribute_provider;
pub mod edit_session;
mod key_event_sink;
mod langbar_item;
pub mod text_input_processor;
mod thread_mgr_event_sink;
use crate::ui::notifier;

use std::sync::OnceLock;
static TEXT_SERVICE_RAW: OnceLock<usize> = OnceLock::new();

fn on_ai_notify() {
    if let Some(raw) = TEXT_SERVICE_RAW.get() {
        unsafe {
            let service = &*(*raw as *const TextService);
            if let Ok(mut inner) = service.write() {
                // 确保有 context
                if inner.context.is_none() {
                    if let Ok(tm) = inner.thread_mgr() {
                        if let Ok(doc_mgr) = tm.GetFocus() {
                            if let Ok(top_ctx) = doc_mgr.GetTop() {
                                inner.context = Some(top_ctx);
                            }
                        }
                    }
                }
                if let Some(rx) = inner.ai_rx.as_ref() {
                    if let Ok(text) = rx.try_recv() {
                        let _ = inner.show_ai_completion(text);
                    }
                }
            }
        }
    }
}

use std::{ffi::OsString, time::{Duration, Instant}};

use log::{debug, error, warn};
use log_derive::logfn;
use parking_lot::{RwLock, RwLockWriteGuard};
use std::sync::mpsc::{self, Receiver, Sender};
use windows::{
    Win32::{
        Foundation::{BOOL, FALSE, TRUE, E_FAIL},
        UI::{
            TextServices::{
                HKL, ITfComposition, ITfCompositionSink, ITfContext, ITfDisplayAttributeProvider,
                ITfKeyEventSink, ITfLangBarItem, ITfRange, ITfTextInputProcessor, ITfTextInputProcessorEx,
                ITfThreadMgr, ITfThreadMgrEventSink,
            },
            WindowsAndMessaging::HICON,
        },
    },
    core::{AsImpl, Interface, Result, VARIANT, implement},
};

use crate::{
    ai_completion::AiCompletionManager,
    engine::{RimeEngine, Suggestion},
    extend::OsStrExt2,
    global::hkl_or_us,
    ui::candidate_list::CandidateList,
    hybrid_context::HybridContextProvider,
    conf,
};

//----------------------------------------------------------------------------
//
//  A text service is required to implement ITfTextInputProcessor and provide
//  a few other interfaces in ITfTextInputProcessor::Activate. The common
//  approach is to let the text service implement every interfaces needed and
//  return self whenever required.
//
//----------------------------------------------------------------------------

/// Methods of TSF interfaces don't allow mutation of any kind. Thus all mutable
/// states are hidden behind a lock. The lock is supposed to be light-weight since
/// inputs from users can be frequent.
#[implement(
    ITfTextInputProcessor,
    ITfTextInputProcessorEx,
    ITfThreadMgrEventSink,
    ITfKeyEventSink,
    ITfCompositionSink,
    ITfLangBarItem,
    ITfDisplayAttributeProvider
)]
pub struct TextService {
    inner: RwLock<TextServiceInner>,
}
struct TextServiceInner {
    // engine
    engine: RimeEngine,
    // Some basic info about the clinet (the program where user is typing)
    tid: u32,
    thread_mgr: Option<ITfThreadMgr>,
    context: Option<ITfContext>,
    // ThreadMrgEventSink
    cookie: Option<u32>,
    // KeyEventSink
    hkl: HKL,
    char_buf: String,
    fresh_ctrl: bool,
    disabled_by_ctrl: bool,
    // Composition
    composition: Option<ITfComposition>,
    spelling: String,
    selected: String,
    suggestions: Vec<Suggestion>,
    preedit: String,
    // paging state
    page: usize,
    // display attribute provider
    display_attribute: Option<VARIANT>,
    // UI
    candidate_list: Option<CandidateList>,
    icon: HICON,
    // An Arc-like smart pointer pointing to TextService
    interface: Option<ITfTextInputProcessor>,
    // AI补全相关状态
    ai_completion: AiCompletionManager,
    ai_completion_text: String,
    ai_completion_visible: bool,
    ai_completion_loading: bool,
    ai_completion_range: Option<ITfRange>,
    last_input_time: Instant,
    ai_trigger_pending: bool,
    // AI异步结果回传信道
    ai_tx: Option<Sender<String>>,
    ai_rx: Option<Receiver<String>>,
    // 混合上下文提供器
    context_provider: HybridContextProvider,
}

impl TextService {
    #[logfn(err = "Error")]
    pub fn create() -> Result<ITfTextInputProcessor> {
        let inner = TextServiceInner {
            engine: RimeEngine::new().unwrap_or_else(|e| {
                log::error!("Failed to create RIME engine: {:?}", e);
                panic!("Cannot create RIME engine");
            }),
            tid: 0,
            thread_mgr: None,
            context: None,
            hkl: hkl_or_us(),
            char_buf: String::with_capacity(4),
            fresh_ctrl: false,
            disabled_by_ctrl: false,
            cookie: None,
            composition: None,
            spelling: String::with_capacity(32),
            suggestions: Vec::new(),
            selected: String::with_capacity(32),
            preedit: String::with_capacity(32),
            page: 0,
            icon: HICON::default(),
            candidate_list: None,
            display_attribute: None,
            interface: None,
            // 初始化AI补全相关状态
            ai_completion: AiCompletionManager::new(conf::get().ai_completion.clone()),
            ai_completion_text: String::new(),
            ai_completion_visible: false,
            ai_completion_loading: false,
            ai_completion_range: None,
            last_input_time: Instant::now(),
            ai_trigger_pending: false,
            ai_tx: None,
            ai_rx: None,
            // 初始化混合上下文提供器
            context_provider: HybridContextProvider::new(),
        };
        let text_service = TextService {
            inner: RwLock::new(inner),
        };
        // from takes ownership of the object and returns a smart pointer
        let interface = ITfTextInputProcessor::from(text_service);
        // inject the smart pointer back to the object
        let text_service: &TextService = unsafe { interface.as_impl() };
        text_service.write()?.interface = Some(interface.clone());
        // 初始化隐藏通知窗口（用于AI结果即时回显）
        let _ = crate::ui::notifier::ensure_created();
        crate::ui::notifier::set_callback(on_ai_notify);
        // 保存 TextService 地址供回调使用
        let _ = TEXT_SERVICE_RAW.set(text_service as *const _ as usize);
        // cast the interface to desired type
        interface.cast()
    }

    fn write(&self) -> Result<RwLockWriteGuard<'_, TextServiceInner>> {
        self.inner
            .try_write()
            .or_else(|| {
                warn!("RwLock::try_write returned None.");
                let timeout = Instant::now() + Duration::from_millis(50);
                self.inner.try_write_until(timeout)
            })
            .ok_or_else(|| {
                error!("Failed to obtain write lock.");
                E_FAIL.into()
            })
    }

    fn try_write(&self) -> Result<RwLockWriteGuard<'_, TextServiceInner>> {
        self.inner.try_write().ok_or_else(|| E_FAIL.into())
    }
}

impl TextServiceInner {
    fn interface<I: Interface>(&self) -> Result<I> {
        // guarenteed to be Some by TextService::create
        self.interface.as_ref().unwrap().cast()
    }

    fn thread_mgr(&self) -> Result<&ITfThreadMgr> {
        self.thread_mgr.as_ref().ok_or_else(|| {
            error!("Thread manager is None.");
            E_FAIL.into()
        })
    }

    fn context(&self) -> Result<&ITfContext> {
        self.context.as_ref().ok_or_else(|| {
            error!("Context is None.");
            E_FAIL.into()
        })
    }

    fn candidate_list(&self) -> Result<&CandidateList> {
        self.candidate_list.as_ref().ok_or(E_FAIL.into())
    }

    fn create_candidate_list(&mut self) -> Result<()> {
        let parent_window = unsafe {
            self.thread_mgr()?
                .GetFocus()?
                .GetTop()?
                .GetActiveView()?
                .GetWnd()?
        };
        self.candidate_list = Some(CandidateList::create(parent_window)?);
        Ok(())
    }

    fn assure_candidate_list(&mut self) -> Result<()> {
        if self.candidate_list.is_some() {
            Ok(())
        } else {
            debug!("Previous creation of candidate list failed. Recreating now.");
            self.create_candidate_list()
        }
    }

    /// 触发AI补全
    fn trigger_ai_completion(&mut self) {
        if !self.ai_completion.is_enabled() {
            debug!("AI completion is not enabled");
            return;
        }
        
        if self.composition.is_some() {
            debug!("Cannot trigger AI completion while in composition mode");
            return;
        }

        // 确保有上下文：若缺失，尝试从 ThreadMgr 获取聚焦 context
        if self.context.is_none() {
            debug!("Context is None, attempting to get focused context from ThreadMgr");
            if let Ok(tm) = self.thread_mgr() {
                unsafe {
                    if let Ok(doc_mgr) = tm.GetFocus() {
                        if let Ok(top_ctx) = doc_mgr.GetTop() {
                            self.context = Some(top_ctx);
                            debug!("Successfully obtained context from ThreadMgr");
                        } else {
                            debug!("Failed to get top context from document manager");
                        }
                    } else {
                        debug!("Failed to get focused document manager");
                    }
                }
            } else {
                debug!("Failed to get thread manager");
            }
        }

        debug!("Triggering AI completion");
        self.ai_completion_loading = true;

        // 显示加载指示器（无侵入）
        if let Err(e) = self.show_loading_indicator() {
            error!("Failed to show loading indicator: {:?}", e);
        }

        // 获取上下文文本 - 使用混合上下文提供器
        if let Some(ai_config) = &conf::get().ai_completion {
            match self.context_provider.get_context_before_cursor(
                self.tid,
                self.context.as_ref(),
                ai_config.max_context_length,
            ) {
                Ok(context_text) if !context_text.trim().is_empty() => {
                    debug!(
                        "Got context text using {} method (length: {}): '{}'", 
                        self.context_provider.get_last_method_used(),
                        context_text.len(), 
                        context_text.chars().take(50).collect::<String>()
                    );
                    // 启动异步AI补全任务
                    self.start_ai_completion_task(context_text);
                }
                Ok(_) => {
                    debug!(
                        "Context text is empty using {} method - skipping AI completion",
                        self.context_provider.get_last_method_used()
                    );
                    self.ai_completion_loading = false;
                    self.clear_ai_completion().ok();
                }
                Err(e) => {
                    debug!(
                        "Failed to get context text using {} method: {} - skipping AI completion",
                        self.context_provider.get_last_method_used(),
                        e
                    );
                    self.ai_completion_loading = false;
                    self.clear_ai_completion().ok();
                }
            }
        }
    }

    /// 启动AI补全异步任务
    fn start_ai_completion_task(&mut self, context: String) {
        // 这里需要使用异步运行时来执行AI补全请求
        // 由于TSF环境的限制，我们需要在后台线程中执行
        debug!("Starting AI completion task with context: '{}'", context.chars().take(50).collect::<String>());

        // 为回传结果准备信道（若未初始化）
        if self.ai_tx.is_none() || self.ai_rx.is_none() {
            let (tx, rx) = mpsc::channel::<String>();
            self.ai_tx = Some(tx);
            self.ai_rx = Some(rx);
        }
        let tx = self.ai_tx.as_ref().cloned();

        // 创建AI补全管理器的克隆用于后台任务
        let ai_completion = self.ai_completion.clone();

        // 在后台线程中执行AI补全请求
        std::thread::spawn(move || {
            let Some(tx) = tx else { 
                error!("AI completion channel not available");
                return; 
            };
            
            // 启动时先发一个消息以确保窗口已创建
            crate::ui::notifier::post_notify();
            
            // 创建一个简单的tokio运行时
            let rt = match tokio::runtime::Runtime::new() {
                Ok(rt) => rt,
                Err(e) => {
                    error!("Failed to create tokio runtime: {:?}", e);
                    let _ = tx.send(String::new()); // 发送空结果表示失败
                    return;
                }
            };

            // 执行异步AI补全请求
            rt.block_on(async move {
                match ai_completion.get_completion(&context).await {
                    Some(completion) if !completion.trim().is_empty() => {
                        debug!("AI completion received: '{}'", completion.chars().take(50).collect::<String>());
                        let _ = tx.send(completion);
                        // 通知主线程即时回显
                        crate::ui::notifier::post_notify();
                    },
                    Some(completion) => {
                        debug!("AI completion returned empty/whitespace result: '{}'", completion);
                        let _ = tx.send(String::new());
                    },
                    None => {
                        debug!("AI completion request failed or returned None");
                        let _ = tx.send(String::new());
                    }
                }
            });
        });
    }

    /// 设置空闲触发定时器
    fn schedule_ai_completion_trigger(&mut self) {
        if let Some(ai_config) = &conf::get().ai_completion {
            if ai_config.enabled && ai_config.trigger_on_idle && self.composition.is_none() {
                self.ai_trigger_pending = true;
                debug!("Scheduled AI completion trigger for idle timeout");
            }
        }
    }

    /// 检查是否应该触发空闲AI补全
    fn check_idle_ai_completion(&mut self) {
        if !self.ai_trigger_pending || self.composition.is_some() {
            return;
        }

        if let Some(ai_config) = &conf::get().ai_completion {
            let elapsed = self.last_input_time.elapsed();
            if elapsed >= Duration::from_millis(ai_config.trigger_delay_ms) {
                self.ai_trigger_pending = false;
                self.trigger_ai_completion();
            }
        }
    }

    /// 接受AI补全
    fn accept_ai_completion(&mut self) -> Result<BOOL> {
        if !self.ai_completion_visible || self.ai_completion_text.is_empty() {
            return Ok(FALSE);
        }

        debug!("Accepting AI completion: {}", self.ai_completion_text);

        // 先清除预览显示，避免在插入时产生视觉闪烁
        let completion_text = self.ai_completion_text.clone();
        self.clear_ai_completion()?;

        // 插入补全文本
        if let Some(context) = &self.context {
            let text = OsString::from(&completion_text).to_wchars();
            edit_session::insert_text(self.tid, context, &text)?;
        }

        Ok(TRUE)
    }

    /// 清除AI补全显示
    fn clear_ai_completion(&mut self) -> Result<()> {
        if self.ai_completion_visible {
            debug!("Clearing AI completion display");

            // 清除显示的补全文本
            if let (Some(context), Some(range)) = (&self.context, &self.ai_completion_range) {
                if let Err(e) = edit_session::clear_ai_completion_text(self.tid, context, range) {
                    error!("Failed to clear AI completion text: {:?}", e);
                }
            }

            self.ai_completion_visible = false;
            self.ai_completion_text.clear();
            self.ai_completion_range = None;
        }
        Ok(())
    }

    /// 显示AI补全文本
    fn show_ai_completion(&mut self, completion_text: String) -> Result<()> {
        debug!("Showing AI completion: {}", completion_text);

        // 先清除之前的补全显示
        self.clear_ai_completion()?;

        if let Some(context) = &self.context {
            // 优先尝试灰色预览显示，这种方式不会影响文档内容
            match edit_session::show_ai_completion_text(
                self.tid,
                context,
                &completion_text,
                self.display_attribute.as_ref(),
            ) {
                Ok(range) => {
                    // 灰色预览成功
                    self.ai_completion_text = completion_text;
                    self.ai_completion_visible = true;
                    self.ai_completion_loading = false;
                    self.ai_completion_range = Some(range);
                    debug!("AI completion text displayed successfully with preview");
                }
                Err(e) => {
                    // 灰色预览失败，不使用 fallback 直接提交
                    // 这样可以避免在 Word 等应用中的重复插入问题
                    error!("Failed to show AI completion text: {:?}", e);
                    debug!("AI completion preview failed - skipping fallback to avoid duplicate insertion");
                    
                    // 清理状态
                    self.ai_completion_text.clear();
                    self.ai_completion_visible = false;
                    self.ai_completion_loading = false;
                    self.ai_completion_range = None;
                }
            }
        }

        Ok(())
    }

    /// 显示加载指示器
    fn show_loading_indicator(&mut self) -> Result<()> {
        debug!("Showing AI completion loading indicator (suppressed to avoid polluting user text)");
        // 不再向宿主文档插入任何占位文本，避免污染用户编辑区。
        // 仅维护内部状态；若将来实现了非侵入式UI（悬浮窗/候选窗提示），可在此处显示。
        self.ai_completion_text.clear();
        self.ai_completion_visible = false;
        self.ai_completion_range = None;
        Ok(())
    }
}

//----------------------------------------------------------------------------
//
//  Now see tsf/text_input_processor.rs for the implementation.
//
//----------------------------------------------------------------------------
