# aiRustIme - 基于 Rust 的中文拼音输入法

本项目是一个使用 [Rust](https://www.rust-lang.org/) 语言和 Windows Text Services Framework (TSF) 构建的中文拼音输入法。它提供了一个基于拼音的中文候选词选择输入体验。

![功能预览](./doc/preview.gif)

## 🌟 特性

- **纯 Rust 实现**: 核心逻辑完全由 Rust 编写，保证了内存安全和高性能。
- **Windows TSF 集成**: 作为标准的 Windows 输入法，兼容各类应用程序。
- **拼音输入**: 支持全拼输入，并提供候选词列表。
- **高度可定制**:
    - **自定义词典**: 用户可以方便地添加、修改自己的词典。
    - **外观配置**: 候选框的颜色、字体、字号、横/竖排版均可配置。
- **中英文切换**: 支持快捷键快速切换中/英文输入模式。
- **开箱即用**: 提供安装程序，无需复杂配置。

## 🚀 安装

我们推荐通过预编译的安装包进行安装（如果已提供）。您也可以按照下面的 “从源码构建” 指南自行构建。

## 🛠️ 使用方法

1.  **切换输入法**:
    - 安装后，在 Windows 语言设置中添加本输入法。
    - 使用 <kbd>Win</kbd> + <kbd>Space</kbd> 快捷键切换到 `aiRustIme`。

2.  **输入文字**:
    - 直接键入小写字母进行拼音输入。
    - 按 <kbd>空格键</kbd> 选择第一个候选词，或按 <kbd>1</kbd> ~ <kbd>5</kbd> 选择对应的候选词。
    - 按 <kbd>回车键</kbd> 将当前输入的英文字符直接上屏。

3.  **中/英文模式切换**:
    - 在输入法激活状态下，按 <kbd>Ctrl</kbd> 键可以在中文和英文输入模式间切换。

4.  **标点符号**:
    - 输入法会自动处理中英文标点符号。

## ⚙️ 配置

输入法的配置文件和词典文件位于 `%APPDATA%/aiRustIme/` 目录下。

### 词典 (`dict/chinese.dict`)

词典文件采用纯文本格式，规则如下:

1.  每一行代表一个拼音的所有候选字。格式为: `{pinyin} {候选字1} {候选字2} ...`
2.  以 `#` 开头的行为注释行。

例如:
```
# 这是一个词典示例
ni     你 尼 泥
hao    好 号 豪
ma     吗 妈 马
```

### 外观及行为 (`conf.toml`)

通过编辑 `conf.toml` 文件，您可以自定义输入法的外观和行为。

```Toml
# 字体设置
[font]
name = "Microsoft YaHei" # 候选框字体
size = 20                 # 字体大小

# 布局设置
[layout]
vertical = false # false 为横向候选框, true 为竖向

# 颜色配置 (Hex 格式)
[color]
clip = "#0078D7"         # 已输入字符串的背景色
background = "#FAFAFA"   # 候选框背景色
highlight = "#E8E8FF"    # 高亮候选词的背景色
index = "#A0A0A0"        # 候选词索引的颜色
candidate = "black"      # 候选词文字颜色
highlighted = "black"    # 高亮候选词的文字颜色

# 行为配置
[behavior]
toggle = "Ctrl"      # 中/英文切换键
cjk_space = true     # true: 在中文模式下，空格输出为全角空格
```

## 👨‍💻 从源码构建

### 环境要求

1.  **Rust**: 安装 [Rust](https://www.rust-lang.org/tools/install) (请确保使用 MSVC toolchain)。
2.  **Just**: 一个方便的命令运行工具。
3.  **Inno Setup**: (可选) 用于创建安装程序。

### 构建步骤

1.  **安装 `just`**:
    ```shell
    cargo install just
    ```

2.  **初始化开发环境** (此步骤会自动处理一些依赖):
    ```shell
    just setup
    ```

3.  **构建和注册输入法**:
    此命令会编译项目，并将生成的 DLL 文件注册到 Windows 系统中，方便您立即测试。
    ```shell
    just build
    ```

4.  **取消注册输入法**:
    测试完毕后，可以使用此命令从系统中卸载输入法。
    ```shell
    just unreg
    ```

5.  **打包安装程序**:
    如果您安装了 Inno Setup，可以使用此命令创建一个 `.exe` 安装包。
    ```shell
    just pack
    ```

## 🏗️ 项目架构

- **`src/engine/`**: 输入法核心引擎，负责拼音解析、词典管理和候选词生成。
- **`src/tsf/`**: Windows TSF 接口实现，负责将输入法核心与 Windows 系统服务集成。
- **`src/ui/`**: 候选框的渲染和交互逻辑。
- **`res/dict/`**: 内置的默认词典。
- **`installer.iss`**: Inno Setup 的打包脚本。
- **`justfile`**: `just` 命令的配置文件，包含了所有构建和管理脚本。
