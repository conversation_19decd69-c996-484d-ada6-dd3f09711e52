use std::sync::OnceLock;
use windows::{
    Win32::{
        Foundation::{HWND, LPARAM, LRESULT, WPARAM},
        UI::WindowsAndMessaging::{
            CreateWindowExA, DefWindowProcA, DispatchMessageA, GetMessageA, PostMessageA,
            RegisterClassExA, SetWindowLongPtrA, TranslateMessage, CS_IME, CS_VREDRAW, CS_HREDRAW,
            WNDCLASSEXA, WINDOW_LONG_PTR_INDEX, WM_APP, WS_EX_TOOLWINDOW, WS_EX_NOACTIVATE, WS_POPUP,
        },
    },
    core::{s, PCSTR, Result},
};

use crate::global;

static CLASS_REGISTERED: OnceLock<()> = OnceLock::new();
static NOTIFIER_WND: OnceLock<HWND> = OnceLock::new();
static CALLBACK: OnceLock<fn()> = OnceLock::new();

const WINDOW_CLASS: PCSTR = s!("AI_COMPLETION_NOTIFY");
pub const WM_AI_COMPLETION_NOTIFY: u32 = WM_APP + 0x151;

pub fn set_callback(cb: fn()) {
    let _ = CALLBACK.set(cb);
}

unsafe extern "system" fn wnd_proc(window: HWND, msg: u32, wparam: WPARAM, lparam: LPARAM) -> LRESULT {
    match msg {
        WM_AI_COMPLETION_NOTIFY => {
            if let Some(cb) = CALLBACK.get() {
                cb();
                return LRESULT(0);
            }
            unsafe { DefWindowProcA(window, msg, wparam, lparam) }
        }
        _ => unsafe { DefWindowProcA(window, msg, wparam, lparam) },
    }
}

pub fn ensure_created() -> Result<HWND> {
    if let Some(hwnd) = NOTIFIER_WND.get() {
        return Ok(*hwnd);
    }

    CLASS_REGISTERED.get_or_init(|| unsafe {
        let wcex = WNDCLASSEXA {
            cbSize: std::mem::size_of::<WNDCLASSEXA>() as u32,
            style: CS_IME | CS_HREDRAW | CS_VREDRAW,
            lpfnWndProc: Some(wnd_proc),
            cbClsExtra: 0,
            cbWndExtra: 0,
            hInstance: global::dll_module(),
            hIcon: Default::default(),
            hCursor: Default::default(),
            hbrBackground: Default::default(),
            lpszMenuName: PCSTR::null(),
            lpszClassName: WINDOW_CLASS,
            hIconSm: Default::default(),
        };
        let _ = RegisterClassExA(&wcex);
    });

    let hwnd = unsafe {
        CreateWindowExA(
            WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE,
            WINDOW_CLASS,
            PCSTR::null(),
            WS_POPUP,
            0,
            0,
            0,
            0,
            None,
            None,
            global::dll_module(),
            None,
        )
    };
    let _ = NOTIFIER_WND.set(hwnd);
    Ok(hwnd)
}

pub fn post_notify() {
    if let Some(hwnd) = NOTIFIER_WND.get() {
        unsafe { let _ = PostMessageA(*hwnd, WM_AI_COMPLETION_NOTIFY, WPARAM(0), LPARAM(0)); }
    }
}

