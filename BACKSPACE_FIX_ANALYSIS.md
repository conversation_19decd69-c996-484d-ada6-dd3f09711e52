# 退格键问题深度分析与修复方案

## 问题现状

通过详细测试，我们发现了输入法组合状态下退格键无法删除字符的根本原因：

### 1. RIME状态管理异常
- 输入'n'后preedit显示'nin'（应该是'n'）
- 输入'i'后preedit显示'ni ni'（应该是'ni'）
- 这表明RIME的内部状态管理有严重问题

### 2. 退格键处理失效
- RIME始终返回`Pass`表示未处理退格键
- 即使尝试不同键码（0x08, 0xFF08）都无效
- preedit内容在退格后完全没有变化

### 3. 状态清理不完整
- `clear_composition`方法无法正确清空RIME状态
- 导致后续输入出现累积的异常preedit内容

## 已实施的修复

### 1. 改进状态变化检测
```rust
// 检查是否真的有状态变化
let state_changed = if keycode == 0x08 { // 退格键特殊处理
    let length_decreased = preedit_after.len() < preedit_before.len();
    let became_empty = !preedit_before.is_empty() && preedit_after.is_empty();
    
    // 如果RIME没有正确处理退格键，我们尝试手动处理
    if has_composition_before && !length_decreased && !became_empty {
        warn!("RIME failed to handle backspace, attempting manual handling");
        return true; // 暂时返回true，表示我们"处理"了这个按键
    }
    
    length_decreased || became_empty
} else {
    has_composition_after || has_candidates
};
```

### 2. 增强组合状态清理
```rust
pub fn clear_composition(&self) {
    if let Some(session) = &self.session {
        // 首先尝试提交当前内容
        let _ = session.commit();
        
        // 然后使用Escape键来清空RIME的组合状态
        let escape_key = KeyEvent::new(0x1B as RimeKeyCode, 0 as RimeModifier);
        let _ = session.process_key(escape_key);
        
        // 再次确保清空
        let _ = session.commit();
        
        // 验证状态是否真的被清空了
        let preedit_after = self.get_preedit();
        if !preedit_after.is_empty() {
            warn!("RIME composition not fully cleared, preedit still contains: '{}'", preedit_after);
        }
        
        info!("RIME composition cleared");
    }
}
```

### 3. 退格键强制处理
对于退格键，如果RIME返回`Pass`但存在组合状态，强制返回`true`防止按键传递到应用程序。

## 测试结果

✅ **成功部分**：
- 退格键被正确识别为"已处理"
- 防止了退格键传递到应用程序
- TSF层面的处理逻辑正确

❌ **仍存在问题**：
- RIME内部状态管理异常
- preedit内容没有正确响应退格键
- 组合状态清理不彻底

## 根本原因分析

问题的根本原因可能是：

1. **RIME配置问题**：当前schema可能缺少正确的退格键绑定
2. **RIME版本兼容性**：使用的RIME版本可能有退格键处理的bug
3. **键码映射错误**：Windows键码到RIME键码的映射可能不正确
4. **API使用问题**：可能需要使用不同的RIME API来处理退格键

## 下一步修复建议

### 1. 检查RIME配置文件
查看`pinyin_simp.schema.yaml`中的键绑定配置，确保退格键有正确的绑定。

### 2. 尝试替代方案
- 使用RIME的`simulate_key_sequence`方法
- 尝试直接操作RIME的composition buffer
- 考虑使用不同的键码格式

### 3. 实现手动退格逻辑
如果RIME无法正确处理，考虑在检测到退格键时：
- 手动获取当前preedit内容
- 删除最后一个字符
- 重新设置preedit内容

### 4. 版本回退测试
测试使用较早版本的RIME是否能解决问题。

## 当前状态

修复**部分完成**：
- ✅ 防止了退格键传递到应用程序
- ✅ 改进了状态检测逻辑
- ❌ RIME内部退格键处理仍有问题
- ❌ 需要进一步调查RIME配置和API使用

## 修改的文件

- `src/engine/rime_engine.rs`: 
  - 改进了`process_key`方法的状态检测逻辑
  - 增强了`clear_composition`方法
  - 添加了退格键的特殊处理逻辑
