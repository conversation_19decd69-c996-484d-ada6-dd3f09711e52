use chinese_ime::ai_completion::{AiCompletionManager, AiCompletionClient};
use chinese_ime::conf::{self, AiCompletion};
use log::{debug, info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("debug")).init();
    
    println!("=== AI 补全功能修复测试 ===\n");
    
    // 测试配置加载
    println!("1. 测试配置加载...");
    let config = conf::get();
    match &config.ai_completion {
        Some(ai_config) => {
            println!("✓ AI补全配置已加载");
            println!("  - 启用状态: {}", ai_config.enabled);
            println!("  - API端点: {}", ai_config.api_endpoint);
            println!("  - 模型: {}", ai_config.model);
            println!("  - 最大上下文长度: {}", ai_config.max_context_length);
        }
        None => {
            println!("✗ 未找到AI补全配置");
            return Ok(());
        }
    }
    
    // 测试AI补全管理器创建
    println!("\n2. 测试AI补全管理器初始化...");
    let manager = AiCompletionManager::new(config.ai_completion.clone());
    println!("✓ AI补全管理器已创建，启用状态: {}", manager.is_enabled());
    
    if !manager.is_enabled() {
        println!("⚠ AI补全未启用，请在配置文件中设置 enabled = true");
        println!("配置文件位置: %APPDATA%/ChineseIME/conf.toml");
        return Ok(());
    }
    
    // 测试API连接
    println!("\n3. 测试API连接...");
    let test_contexts = vec![
        "你好，今天天气",
        "人工智能技术",
        "Hello, how are",
        "The quick brown",
        "", // 空上下文测试
    ];
    
    for (i, context) in test_contexts.iter().enumerate() {
        println!("  测试上下文 {}: '{}'", i + 1, context);
        
        if context.is_empty() {
            println!("    → 跳过空上下文测试");
            continue;
        }
        
        match manager.get_completion(context).await {
            Some(completion) if !completion.trim().is_empty() => {
                println!("    ✓ 获得补全: '{}'", completion.chars().take(50).collect::<String>());
            }
            Some(completion) => {
                println!("    ⚠ 获得空补全: '{}'", completion);
            }
            None => {
                println!("    ✗ 补全失败");
            }
        }
        
        // 避免过于频繁的请求
        tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    }
    
    // 测试错误处理
    println!("\n4. 测试错误处理...");
    
    // 创建一个无效配置的客户端
    let invalid_config = AiCompletion {
        enabled: true,
        api_endpoint: "http://invalid-endpoint:99999/v1/chat/completions".to_string(),
        api_key: None,
        model: "invalid-model".to_string(),
        max_tokens: 10,
        temperature: 0.7,
        timeout_seconds: 2, // 短超时
        trigger_delay_ms: 1000,
        max_context_length: 100,
        trigger_on_ctrl: true,
        trigger_on_idle: true,
    };
    
    match AiCompletionClient::new(invalid_config) {
        Ok(client) => {
            println!("  无效客户端已创建，测试连接...");
            match client.get_completion("测试").await {
                Ok(_) => println!("    ⚠ 意外成功"),
                Err(e) => println!("    ✓ 正确处理错误: {:?}", e),
            }
        }
        Err(e) => {
            println!("  ✓ 正确拒绝无效配置: {:?}", e);
        }
    }
    
    println!("\n=== 测试完成 ===");
    println!("如果以上测试都通过，说明AI补全功能的核心逻辑正常工作。");
    println!("实际的TSF集成需要在Windows输入法环境中测试。");
    
    // 输出使用说明
    println!("\n=== 使用指南 ===");
    println!("1. 确保配置文件中 enabled = true");
    println!("2. 启动本地AI服务（如Ollama）");
    println!("3. 在支持的应用中测试：");
    println!("   - 记事本：基本文本输入");
    println!("   - Word：富文本编辑");
    println!("   - VS Code：代码编辑");
    println!("4. 触发方式：");
    println!("   - 按左Ctrl键手动触发");
    println!("   - 停止输入2秒自动触发");
    println!("5. 接受补全：按Tab键");
    
    Ok(())
}