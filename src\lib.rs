#![allow(non_camel_case_types)]
pub mod accessibility_context;
pub mod ai_completion;
pub mod clipboard_context;
pub mod conf;
pub mod engine;
mod extend;
mod global;
pub mod hybrid_context;
mod logger;
mod register;
mod tsf;
mod ui;

use std::{ffi::c_void, num::ParseIntError};

use global::*;
use register::*;
use ui::candidate_list;
use windows::{
    Win32::{
        Foundation::{BOOL, E_FAIL, HINSTANCE, S_FALSE, WIN32_ERROR},
        System::{
            Com::{IClassFactory, IClassFactory_Impl},
            SystemServices::DLL_PROCESS_ATTACH,
        },
    },
    core::{GUID, HRESULT, IUnknown, Interface, implement},
};

use crate::tsf::TextService;

//----------------------------------------------------------------------------
//
//  Entry for the DLL
//
//----------------------------------------------------------------------------

#[unsafe(no_mangle)]
extern "system" fn DllMain(dll_module: HINSTANCE, call_reason: u32, _reserved: *mut ()) -> bool {
    if call_reason != DLL_PROCESS_ATTACH {
        return true;
    }
    logger::setup();
    global::setup(dll_module);
    candidate_list::setup().is_ok()
}

//----------------------------------------------------------------------------
//
//  The four exposed functions.
//
//----------------------------------------------------------------------------

// Register the IME into the OS. See register.rs.
#[unsafe(no_mangle)]
extern "system" fn DllRegisterServer() -> HRESULT {
    fn reg() -> windows::core::Result<()> {
        register_server()?;
        register_ime()?;
        Ok(())
    }
    reg().into()
}

// Unregister the IME from the OS. See register.rs.
#[unsafe(no_mangle)]
extern "system" fn DllUnregisterServer() -> HRESULT {
    fn unreg() -> windows::core::Result<()> {
        unregister_ime()?;
        unregister_server()?;
        Ok(())
    }
    unreg().into()
}

// Returns the required object. For a COM dll like an IME, the required object is always a class factory.
#[unsafe(no_mangle)]
extern "system" fn DllGetClassObject(
    _rclsid: *const GUID,
    riid: *const GUID,
    ppv: *mut *mut c_void,
) -> HRESULT {
    // SomeInterface::from will move the object, thus we don't need to worry about the object's lifetime
    // the return value is a C++ vptr pointing to the moved object under the hood
    // *ppv = mem::transmute(&ClassFactory::new()) is incorrect and causes the Grey Screen of Death.
    unsafe { IUnknown::from(ClassFactory::new()).query(riid, ppv) }
}

#[unsafe(no_mangle)]
extern "system" fn DllCanUnloadNow() -> HRESULT {
    // todo: add ref count.
    // it seems not that of a important thing to do according to
    // https://github.com/microsoft/windows-rs/issues/2472 tho
    S_FALSE
}

//----------------------------------------------------------------------------
//
//  ClassFactory. It creates nothing but IME instances.
//
//----------------------------------------------------------------------------

#[implement(IClassFactory)]
struct ClassFactory;

impl ClassFactory {
    fn new() -> ClassFactory {
        ClassFactory {}
    }
}

impl IClassFactory_Impl for ClassFactory {
    fn CreateInstance(
        &self,
        _punkouter: Option<&IUnknown>,
        riid: *const GUID,
        ppvobject: *mut *mut c_void,
    ) -> windows::core::Result<()> {
        unsafe { TextService::create()?.query(riid, ppvobject).ok() }
    }

    fn LockServer(&self, _flock: BOOL) -> windows::core::Result<()> {
        Ok(())
    }
}

//----------------------------------------------------------------------------
//
//  Error
//
//----------------------------------------------------------------------------

pub type Result<T> = std::result::Result<T, Error>;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error(transparent)]
    Win(#[from] windows::core::Error),
    #[error(transparent)]
    Io(#[from] std::io::Error),
    #[error(transparent)]
    Var(#[from] std::env::VarError),
    // custom ones
    #[error("Language ID is missing from 'install.toml'.")]
    LangidMissing,
    #[error("Keyboad layout is is missing from 'install.toml'.")]
    LayoutMissing,
    #[error("Requested keyboard layout is invalid.")]
    LayoutInvalid,
    #[error("Failed to parse '{0}'. {1:?}")]
    ParseError(&'static str, toml::de::Error),
    #[error("install.dat is corrupted. {0}")]
    InstallDatCorrupted(ParseIntError),
}

// bonus From<E> for alternative windows Error types
impl From<WIN32_ERROR> for Error {
    fn from(value: WIN32_ERROR) -> Self {
        Self::Win(value.into())
    }
}
impl From<HRESULT> for Error {
    fn from(value: HRESULT) -> Self {
        Self::Win(value.into())
    }
}

// cast to windows Error when required, keeping the original error message
impl From<Error> for windows::core::Error {
    fn from(value: Error) -> Self {
        match value {
            Error::Win(e) => e,
            other => windows::core::Error::new(E_FAIL, other.to_string()),
        }
    }
}
impl From<Error> for HRESULT {
    fn from(value: Error) -> Self {
        windows::core::Error::from(value).into()
    }
}

//----------------------------------------------------------------------------
//
//  See tsf/mod.rs for the IME's implementation
//
//----------------------------------------------------------------------------

/// 测试引擎功能的简单函数
pub fn test_engine() {
    use engine::RimeEngine;

    println!("=== 中文输入法引擎测试 ===");

    // 创建 RIME 引擎
    let engine = RimeEngine::new().unwrap_or_else(|e| {
        println!("Failed to create RIME engine: {:?}", e);
        panic!("Cannot create RIME engine");
    });

    println!("RIME 引擎创建成功！");
    println!();

    // 测试 RIME 引擎的基本功能
    println!("=== RIME 引擎测试 ===");

    // 检查初始状态
    println!("检查 RIME 初始状态...");
    if let Some(session) = &engine.session {
        if let Ok(status) = session.status() {
            println!("RIME 状态:");
            println!("  方案: {}", status.schema_name());
            println!("  方案ID: {}", status.schema_id());
            println!("  是否禁用: {}", status.is_disabled);
            println!("  是否在组合: {}", status.is_composing);
            println!("  ASCII模式: {}", status.is_ascii_mode);
            println!("  全角模式: {}", status.is_full_shape);
            println!("  简体模式: {}", status.is_simplified);
            println!("  繁体模式: {}", status.is_traditional);
            println!("  ASCII标点: {}", status.is_ascii_punct);
        }
    }
    println!();

    // 测试按键处理
    println!("测试按键处理...");
    let test_keys = vec!['n' as i32, 'i' as i32];

    for key in test_keys {
        let processed = engine.process_key(key);
        println!("按键 {} 处理结果: {}", key as u8 as char, processed);

        // 每次按键后检查状态
        let preedit = engine.get_preedit();
        let candidates = engine.get_candidates();
        println!("  预编辑文本: '{}'", preedit);
        println!("  候选词数量: {}", candidates.len());
        if !candidates.is_empty() {
            println!("  前5个候选词: {:?}",
                     candidates.iter().take(5).map(|s| &s.output).collect::<Vec<_>>());
        }
        println!();
    }

    // 测试空格键提交
    println!("测试空格键提交...");
    let space_processed = engine.process_key(32); // 空格键
    println!("空格键处理结果: {}", space_processed);

    // 检查是否有提交的文本
    if let Some(commit_text) = engine.commit() {
        println!("提交的文本: '{}'", commit_text);
    } else {
        println!("没有提交的文本");
    }

    println!("测试完成！");
}
