setup:
    rustup target add x86_64-pc-windows-msvc
    rustup target add i686-pc-windows-msvc

# 只构建，不注册DLL
build-only *args:
    cargo build {{args}}
    cargo build --target=i686-pc-windows-msvc {{args}}
fmt:
    cargo +nightly fmt --all
build *args:
    -regsvr32 -s -u  ./target/debug/chinese_ime.dll  2>nul || echo "Unregister failed (expected if not previously registered)"
    -regsvr32 -s -u  ./target/i686-pc-windows-msvc/debug/chinese_ime.dll  2>nul || echo "Unregister failed (expected if not previously registered)"
    cargo build {{args}}
    cargo build --target=i686-pc-windows-msvc {{args}}
    regsvr32 -s ./target/debug/chinese_ime.dll
    regsvr32 -s ./target/i686-pc-windows-msvc/debug/chinese_ime.dll
follow:
    tail -f $LOCALAPPDATA/ChineseIME/log.txt
unreg:
    regsvr32 -s -u  ./target/debug/chinese_ime.dll || true
    regsvr32 -s -u  ./target/i686-pc-windows-msvc/debug/chinese_ime.dll || true
pack:
    cargo build --release
    cargo build --release --target=i686-pc-windows-msvc
    iscc ./installer.iss
release:
    just pack
    git push --delete origin nightly || true
    git tag -d nightly || true
    git tag nightly
    git push origin nightly
    gh release create nightly ./target/release/chinese-ime-installer_x64.exe -t "Nightly Build" -n "Nightly Build"
