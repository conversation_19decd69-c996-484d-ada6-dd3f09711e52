use chinese_ime::ai_completion::{AiCompletionManager, AiCompletionClient};
use chinese_ime::conf::AiCompletion;

#[tokio::main]
async fn main() {
    env_logger::init();
    
    println!("Testing AI Completion functionality...");
    
    // 创建测试配置
    let config = AiCompletion {
        enabled: true,
        api_endpoint: "http://192.168.0.133:11434/v1/chat/completions".to_string(),
        api_key: None,
        model: "qwen2.5:0.5b".to_string(),
        max_tokens: 50,
        temperature: 0.7,
        timeout_seconds: 10,
        trigger_delay_ms: 2000,
        max_context_length: 500,
        trigger_on_ctrl: true,
        trigger_on_idle: true,
    };
    
    // 测试AI补全客户端
    match AiCompletionClient::new(config.clone()) {
        Ok(client) => {
            println!("✓ AI completion client created successfully");
            
            // 测试补全请求
            let test_context = "今天天气很好，我想去";
            println!("Testing completion for context: '{}'", test_context);
            
            match client.get_completion(test_context).await {
                Ok(completion) => {
                    println!("✓ AI completion successful: '{}'", completion);
                }
                Err(e) => {
                    println!("✗ AI completion failed: {:?}", e);
                    println!("Note: Make sure Ollama is running with the specified model");
                }
            }
        }
        Err(e) => {
            println!("✗ Failed to create AI completion client: {:?}", e);
        }
    }
    
    // 测试AI补全管理器
    let manager = AiCompletionManager::new(Some(config));
    if manager.is_enabled() {
        println!("✓ AI completion manager is enabled");
        
        let test_context2 = "人工智能的发展将会";
        println!("Testing manager completion for context: '{}'", test_context2);
        
        match manager.get_completion(test_context2).await {
            Some(completion) => {
                println!("✓ Manager completion successful: '{}'", completion);
            }
            None => {
                println!("✗ Manager completion failed");
            }
        }
    } else {
        println!("✗ AI completion manager is not enabled");
    }
    
    println!("AI Completion test completed.");
}
