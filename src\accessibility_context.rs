use std::ffi::OsString;
use log::{debug, warn, error};
use windows::{
    Win32::{
        Foundation::{HWND, POINT, RECT},
        UI::{
            Accessibility::{
                IUIAutomation, IUIAutomationElement, IUIAutomationTextPattern,
                UIA_TextPatternId, UIA_ValuePatternId, TreeScope_Element,
                IUIAutomationValuePattern, CUIAutomation,
            },
            WindowsAndMessaging::{GetForegroundWindow, GetWindowTextW},
        },
        System::Com::{CoCreateInstance, CoInitialize, CLSCTX_INPROC_SERVER},
    },
    core::{Interface, Result as WinResult, VARIANT, HRESULT},
};

/// 基于Windows Accessibility API的通用上下文获取器
/// 能够在记事本、Word、VS Code等各种应用中获取光标前的文本
pub struct AccessibilityContextProvider {
    automation: Option<IUIAutomation>,
    initialized: bool,
}

impl AccessibilityContextProvider {
    /// 创建新的上下文提供器
    pub fn new() -> Self {
        Self {
            automation: None,
            initialized: false,
        }
    }

    /// 初始化Accessibility API
    pub fn initialize(&mut self) -> Result<(), String> {
        if self.initialized {
            return Ok(());
        }

        unsafe {
            // 初始化COM
            let _ = CoInitialize(None); // 忽略错误，可能已经初始化

            // 创建UI Automation实例
            match CoCreateInstance::<_, IUIAutomation>(&CUIAutomation, None, CLSCTX_INPROC_SERVER) {
                Ok(automation) => {
                    self.automation = Some(automation);
                    self.initialized = true;
                    debug!("Accessibility API initialized successfully");
                    Ok(())
                }
                Err(e) => {
                    error!("Failed to create UI Automation instance: {:?}", e);
                    Err(format!("Failed to initialize Accessibility API: {:?}", e))
                }
            }
        }
    }

    /// 获取当前活动窗口中光标前的文本上下文
    /// 这是主要的公共接口，会尝试多种方法获取上下文
    pub fn get_context_before_cursor(&mut self, max_length: usize) -> Result<String, String> {
        if !self.initialized {
            self.initialize()?;
        }

        let automation = self.automation.as_ref()
            .ok_or_else(|| "Accessibility API not initialized".to_string())?;

        unsafe {
            // 获取前台窗口
            let hwnd = GetForegroundWindow();
            if hwnd.0 == 0 {
                return Err("No foreground window found".to_string());
            }

            // 获取窗口信息用于调试
            let window_title = self.get_window_title(hwnd);
            debug!("Attempting to get context from window: {}", window_title);

            // 方法1: 尝试从聚焦元素获取文本
            if let Ok(context) = self.get_context_from_focused_element(automation, max_length) {
                if !context.trim().is_empty() {
                    debug!("Got context from focused element: '{}' characters", context.len());
                    return Ok(context);
                }
            }

            // 方法2: 尝试从窗口根元素递归查找文本控件
            if let Ok(context) = self.get_context_from_window_element(automation, hwnd, max_length) {
                if !context.trim().is_empty() {
                    debug!("Got context from window element: '{}' characters", context.len());
                    return Ok(context);
                }
            }

            // 方法3: 特殊应用的fallback方法
            if let Ok(context) = self.get_context_fallback_method(hwnd, max_length) {
                if !context.trim().is_empty() {
                    debug!("Got context from fallback method: '{}' characters", context.len());
                    return Ok(context);
                }
            }

            debug!("No context found using any method for window: {}", window_title);
            Ok(String::new())
        }
    }

    /// 方法1: 从当前聚焦的元素获取文本
    unsafe fn get_context_from_focused_element(
        &self,
        automation: &IUIAutomation,
        max_length: usize,
    ) -> Result<String, String> {
        // 获取当前聚焦的元素
        let focused_element = unsafe { automation.GetFocusedElement()
            .map_err(|e| format!("Failed to get focused element: {:?}", e))? };

        // 尝试获取文本模式
        unsafe {
            if let Ok(text_pattern) = self.get_text_pattern(&focused_element) {
                return self.extract_text_before_cursor(&text_pattern, max_length);
            }

            // 尝试获取值模式
            if let Ok(value_pattern) = self.get_value_pattern(&focused_element) {
                return self.extract_value_text(&value_pattern, max_length);
            }
        }

        Err("No supported pattern found on focused element".to_string())
    }

    /// 方法2: 从窗口根元素查找文本控件
    unsafe fn get_context_from_window_element(
        &self,
        automation: &IUIAutomation,
        hwnd: HWND,
        max_length: usize,
    ) -> Result<String, String> {
        // 从窗口句柄获取UI元素
        let window_element = unsafe { automation.ElementFromHandle(hwnd)
            .map_err(|e| format!("Failed to get element from window: {:?}", e))? };

        // 递归查找文本控件
        unsafe { self.find_text_controls_recursive(&window_element, max_length, 0) }
    }

    /// 方法3: 特殊应用的fallback方法
    unsafe fn get_context_fallback_method(
        &self,
        hwnd: HWND,
        max_length: usize,
    ) -> Result<String, String> {
        let window_title = unsafe { self.get_window_title(hwnd) };
        
        // 针对已知的简单应用使用特殊方法
        if window_title.to_lowercase().contains("notepad") || 
           window_title.to_lowercase().contains("记事本") {
            return unsafe { self.get_context_from_simple_editor(hwnd, max_length) };
        }

        Err("No fallback method available for this application".to_string())
    }

    /// 从文本模式中提取光标前的文本
    unsafe fn extract_text_before_cursor(
        &self,
        text_pattern: &IUIAutomationTextPattern,
        max_length: usize,
    ) -> Result<String, String> {
        // 获取整个文档范围
        let document_range = unsafe { text_pattern.DocumentRange()
            .map_err(|e| format!("Failed to get document range: {:?}", e))? };

        // 获取选择范围（光标位置）
        let selection_ranges = unsafe { text_pattern.GetSelection()
            .map_err(|e| format!("Failed to get selection: {:?}", e))? };

        let length = unsafe { selection_ranges.Length() }
            .map_err(|e| format!("Failed to get selection length: {:?}", e))?;
        
        if length == 0 {
            return Err("No selection found".to_string());
        }

        let selection_range = unsafe { selection_ranges.GetElement(0) }
            .map_err(|e| format!("Failed to get selection range: {:?}", e))?;

        // 创建从文档开始到光标位置的范围
        let context_range = unsafe { document_range.Clone()
            .map_err(|e| format!("Failed to clone document range: {:?}", e))? };

        // 将结束位置移动到光标位置
        unsafe {
            context_range.MoveEndpointByRange(
                windows::Win32::UI::Accessibility::TextPatternRangeEndpoint_End,
                &selection_range,
                windows::Win32::UI::Accessibility::TextPatternRangeEndpoint_Start,
            ).map_err(|e| format!("Failed to move endpoint: {:?}", e))?
        };

        // 获取文本
        let full_text = unsafe { context_range.GetText(-1)
            .map_err(|e| format!("Failed to get text: {:?}", e))? };

        let context = full_text.to_string();
        
        // 限制长度并返回
        if context.len() > max_length {
            let truncated = context.chars()
                .rev()
                .take(max_length)
                .collect::<String>()
                .chars()
                .rev()
                .collect();
            Ok(truncated)
        } else {
            Ok(context)
        }
    }

    /// 从值模式中提取文本
    unsafe fn extract_value_text(
        &self,
        value_pattern: &IUIAutomationValuePattern,
        max_length: usize,
    ) -> Result<String, String> {
        let value = unsafe { value_pattern.CurrentValue()
            .map_err(|e| format!("Failed to get value: {:?}", e))? };

        let text = value.to_string();
        
        // 对于值模式，我们只能获取全部文本，无法确定光标位置
        // 作为fallback，返回最后max_length个字符
        if text.len() > max_length {
            let truncated = text.chars()
                .rev()
                .take(max_length)
                .collect::<String>()
                .chars()
                .rev()
                .collect();
            Ok(truncated)
        } else {
            Ok(text)
        }
    }

    /// 递归查找文本控件
    unsafe fn find_text_controls_recursive(
        &self,
        element: &IUIAutomationElement,
        max_length: usize,
        depth: usize,
    ) -> Result<String, String> {
        // 限制递归深度避免无限递归
        if depth > 5 {
            return Err("Max recursion depth reached".to_string());
        }

        // 尝试从当前元素获取文本
        unsafe {
            if let Ok(text_pattern) = self.get_text_pattern(element) {
                if let Ok(context) = self.extract_text_before_cursor(&text_pattern, max_length) {
                    if !context.trim().is_empty() {
                        return Ok(context);
                    }
                }
            }

            if let Ok(value_pattern) = self.get_value_pattern(element) {
                if let Ok(context) = self.extract_value_text(&value_pattern, max_length) {
                    if !context.trim().is_empty() {
                        return Ok(context);
                    }
                }
            }
        }

        // 递归搜索子元素
        // 这里可以添加子元素遍历逻辑，但为了简化暂时省略

        Err("No text found in element tree".to_string())
    }

    /// 针对简单编辑器的特殊处理
    unsafe fn get_context_from_simple_editor(
        &self,
        _hwnd: HWND,
        _max_length: usize,
    ) -> Result<String, String> {
        // 这里可以实现针对记事本等简单应用的特殊逻辑
        // 比如发送Ctrl+A获取全部文本等
        warn!("Simple editor context extraction not fully implemented");
        Err("Simple editor extraction not implemented".to_string())
    }

    /// 获取文本模式接口
    unsafe fn get_text_pattern(&self, element: &IUIAutomationElement) -> WinResult<IUIAutomationTextPattern> {
        let pattern = unsafe { element.GetCurrentPattern(UIA_TextPatternId)? };
        pattern.cast::<IUIAutomationTextPattern>()
    }

    /// 获取值模式接口
    unsafe fn get_value_pattern(&self, element: &IUIAutomationElement) -> WinResult<IUIAutomationValuePattern> {
        let pattern = unsafe { element.GetCurrentPattern(UIA_ValuePatternId)? };
        pattern.cast::<IUIAutomationValuePattern>()
    }

    /// 获取窗口标题
    unsafe fn get_window_title(&self, hwnd: HWND) -> String {
        let mut buffer = [0u16; 512];
        let len = unsafe { GetWindowTextW(hwnd, &mut buffer) };
        if len > 0 {
            String::from_utf16_lossy(&buffer[..len as usize])
        } else {
            "Unknown Window".to_string()
        }
    }
}

impl Default for AccessibilityContextProvider {
    fn default() -> Self {
        Self::new()
    }
}

/// 辅助功能权限检查
pub fn check_accessibility_permissions() -> bool {
    // 这里可以添加检查当前应用是否有辅助功能权限的逻辑
    // 暂时返回true，实际使用时会在初始化时发现权限问题
    true
}

/// 提示用户启用辅助功能权限
pub fn prompt_accessibility_permissions() -> String {
    "为了在所有应用中获取文本上下文，请启用辅助功能权限：\n\
     1. 打开设置 > 隐私和安全性 > 辅助功能\n\
     2. 找到并启用此输入法的辅助功能权限\n\
     3. 重启输入法使权限生效".to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_accessibility_provider_creation() {
        let provider = AccessibilityContextProvider::new();
        assert!(!provider.initialized);
    }

    #[test]
    fn test_permissions_check() {
        assert!(check_accessibility_permissions());
    }
}