use std::sync::{Arc, Mutex};
use log::{debug, warn, error, info};
use windows::Win32::UI::TextServices::ITfContext;

use crate::{
    accessibility_context::AccessibilityContextProvider,
    clipboard_context::ClipboardContextProvider,
    tsf,
};

/// 混合上下文获取器
/// 结合TSF、Windows Accessibility API和剪贴板方法，提供最佳的兼容性
/// 优先使用TSF方法，然后fallback到Accessibility API，最后使用剪贴板方法
pub struct HybridContextProvider {
    accessibility_provider: Arc<Mutex<AccessibilityContextProvider>>,
    clipboard_provider: Arc<Mutex<ClipboardContextProvider>>,
    tsf_enabled: bool,
    accessibility_enabled: bool,
    clipboard_enabled: bool,
    last_method_used: String,
}

impl HybridContextProvider {
    /// 创建新的混合上下文获取器
    pub fn new() -> Self {
        let mut provider = Self {
            accessibility_provider: Arc::new(Mutex::new(AccessibilityContextProvider::new())),
            clipboard_provider: Arc::new(Mutex::new(ClipboardContextProvider::new())),
            tsf_enabled: true,
            accessibility_enabled: true,
            clipboard_enabled: false, // 默认禁用，因为它会干扰用户剪贴板
            last_method_used: "none".to_string(),
        };

        // 尝试初始化Accessibility API
        provider.initialize_accessibility();
        provider
    }

    /// 初始化Accessibility API
    fn initialize_accessibility(&mut self) {
        if let Ok(mut provider) = self.accessibility_provider.lock() {
            match provider.initialize() {
                Ok(()) => {
                    self.accessibility_enabled = true;
                    info!("Accessibility API initialized successfully");
                }
                Err(e) => {
                    self.accessibility_enabled = false;
                    warn!("Failed to initialize Accessibility API: {}. Will only use TSF.", e);
                }
            }
        } else {
            self.accessibility_enabled = false;
            error!("Failed to lock accessibility provider mutex");
        }
    }

    /// 获取光标前的上下文文本
    /// 这是主要的公共接口，会智能选择最佳方法
    pub fn get_context_before_cursor(
        &mut self,
        tid: u32,
        context: Option<&ITfContext>,
        max_length: usize,
    ) -> Result<String, String> {
        debug!("Attempting to get context using hybrid provider (max_length: {})", max_length);

        // 方法1: 优先尝试TSF方法（当有TSF上下文时）
        if self.tsf_enabled && context.is_some() {
            match self.try_tsf_method(tid, context.unwrap(), max_length) {
                Ok(text) if !text.trim().is_empty() => {
                    self.last_method_used = "TSF".to_string();
                    debug!("Successfully got context using TSF method: {} chars", text.len());
                    return Ok(text);
                }
                Ok(_) => debug!("TSF method returned empty context"),
                Err(e) => debug!("TSF method failed: {}", e),
            }
        } else if context.is_none() {
            debug!("No TSF context available, skipping TSF method");
        }

        // 方法2: Fallback到Accessibility API
        if self.accessibility_enabled {
            match self.try_accessibility_method(max_length) {
                Ok(text) if !text.trim().is_empty() => {
                    self.last_method_used = "Accessibility".to_string();
                    debug!("Successfully got context using Accessibility method: {} chars", text.len());
                    return Ok(text);
                }
                Ok(_) => debug!("Accessibility method returned empty context"),
                Err(e) => debug!("Accessibility method failed: {}", e),
            }
        }

        // 所有方法都失败了
        self.last_method_used = "failed".to_string();
        debug!("All context extraction methods failed");
        Ok(String::new()) // 返回空字符串而不是错误，这样AI补全可以继续工作
    }

    /// 尝试使用TSF方法获取上下文
    fn try_tsf_method(
        &self,
        tid: u32,
        context: &ITfContext,
        max_length: usize,
    ) -> Result<String, String> {
        match tsf::edit_session::get_context_before_cursor(tid, context, max_length) {
            Ok(text) => {
                debug!("TSF method extracted {} characters", text.len());
                Ok(text)
            }
            Err(e) => {
                debug!("TSF context extraction failed: {:?}", e);
                Err(format!("TSF extraction failed: {:?}", e))
            }
        }
    }

    /// 尝试使用Accessibility API方法获取上下文
    fn try_accessibility_method(&self, max_length: usize) -> Result<String, String> {
        if let Ok(mut provider) = self.accessibility_provider.lock() {
            match provider.get_context_before_cursor(max_length) {
                Ok(text) => {
                    debug!("Accessibility method extracted {} characters", text.len());
                    Ok(text)
                }
                Err(e) => {
                    debug!("Accessibility context extraction failed: {}", e);
                    Err(e)
                }
            }
        } else {
            Err("Failed to lock accessibility provider".to_string())
        }
    }

    /// 获取最后使用的方法名称（用于调试）
    pub fn get_last_method_used(&self) -> &str {
        &self.last_method_used
    }

    /// 检查各种方法的可用性
    pub fn check_availability(&mut self) -> ContextAvailability {
        // 重新检查Accessibility API
        if !self.accessibility_enabled {
            self.initialize_accessibility();
        }

        ContextAvailability {
            tsf_available: self.tsf_enabled,
            accessibility_available: self.accessibility_enabled,
            hybrid_ready: self.tsf_enabled || self.accessibility_enabled,
        }
    }

    /// 禁用TSF方法（用于测试或故障排除）
    pub fn disable_tsf(&mut self) {
        self.tsf_enabled = false;
        info!("TSF context extraction disabled");
    }

    /// 启用TSF方法
    pub fn enable_tsf(&mut self) {
        self.tsf_enabled = true;
        info!("TSF context extraction enabled");
    }

    /// 禁用Accessibility方法（用于测试或故障排除）
    pub fn disable_accessibility(&mut self) {
        self.accessibility_enabled = false;
        info!("Accessibility context extraction disabled");
    }

    /// 启用Accessibility方法
    pub fn enable_accessibility(&mut self) {
        if !self.accessibility_enabled {
            self.initialize_accessibility();
        }
    }

    /// 获取详细的诊断信息
    pub fn get_diagnostics(&self) -> String {
        format!(
            "Hybrid Context Provider Diagnostics:\n\
             - TSF enabled: {}\n\
             - Accessibility enabled: {}\n\
             - Last method used: {}\n\
             - Provider status: {}",
            self.tsf_enabled,
            self.accessibility_enabled,
            self.last_method_used,
            if self.tsf_enabled || self.accessibility_enabled {
                "Ready"
            } else {
                "No methods available"
            }
        )
    }
}

/// 上下文获取方法的可用性状态
#[derive(Debug, Clone)]
pub struct ContextAvailability {
    pub tsf_available: bool,
    pub accessibility_available: bool,
    pub hybrid_ready: bool,
}

impl Default for HybridContextProvider {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hybrid_provider_creation() {
        let provider = HybridContextProvider::new();
        assert!(provider.tsf_enabled);
        // accessibility_enabled状态取决于系统环境
    }

    #[test]
    fn test_availability_check() {
        let mut provider = HybridContextProvider::new();
        let availability = provider.check_availability();
        
        // TSF应该总是可用的
        assert!(availability.tsf_available);
        
        // 至少有一种方法应该可用
        assert!(availability.hybrid_ready);
    }

    #[test]
    fn test_method_toggle() {
        let mut provider = HybridContextProvider::new();
        
        // 测试禁用TSF
        provider.disable_tsf();
        assert!(!provider.tsf_enabled);
        
        // 测试重新启用TSF
        provider.enable_tsf();
        assert!(provider.tsf_enabled);
    }

    #[test]
    fn test_diagnostics() {
        let provider = HybridContextProvider::new();
        let diagnostics = provider.get_diagnostics();
        
        assert!(diagnostics.contains("Hybrid Context Provider Diagnostics"));
        assert!(diagnostics.contains("TSF enabled"));
        assert!(diagnostics.contains("Accessibility enabled"));
    }
}