use std::{env, io, path::PathBuf};
use winres::WindowsResource;

fn main() -> io::Result<()> {
    // 仅在 Windows 平台下执行
    if env::var_os("CARGO_CFG_WINDOWS").is_some() {
        // 设置程序图标
        WindowsResource::new()
            .set_icon_with_id("res/tray_lite.ico", "0")
            .set_icon_with_id("res/tray_dark.ico", "1")
            .compile()?;

        // 检测目标架构
        let arch = env::var("CARGO_CFG_TARGET_POINTER_WIDTH").unwrap_or("64".to_string());

        // 根据架构选择库目录
        let lib_dir: PathBuf = if arch == "64" {
            PathBuf::from("deps/x64")
        } else {
            PathBuf::from("deps/x86")
        };

        // 输出给 cargo，添加链接搜索路径
        println!("cargo:rustc-link-search=native={}", lib_dir.display());
        // 告诉 rustc 链接 rime.dll (对应的 rime.lib 会被用到)
        println!("cargo:rustc-link-lib=dylib=rime");
    }

    Ok(())
}