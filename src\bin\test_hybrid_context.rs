use chinese_ime::{
    hybrid_context::HybridContextProvider,
    accessibility_context::{check_accessibility_permissions, prompt_accessibility_permissions},
    conf,
};
use log::{debug, info, warn, error};
use std::{io, thread, time::Duration};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("debug")).init();
    
    println!("=== 混合上下文获取器测试 ===\n");
    
    // 检查权限
    println!("1. 检查辅助功能权限...");
    if !check_accessibility_permissions() {
        println!("⚠ 辅助功能权限检查失败");
        println!("{}", prompt_accessibility_permissions());
        println!("继续测试（可能会失败）...\n");
    } else {
        println!("✓ 辅助功能权限检查通过\n");
    }
    
    // 创建混合上下文提供器
    println!("2. 初始化混合上下文提供器...");
    let mut provider = HybridContextProvider::new();
    
    // 检查可用性
    let availability = provider.check_availability();
    println!("✓ 混合上下文提供器已创建");
    println!("  - TSF 可用: {}", availability.tsf_available);
    println!("  - Accessibility 可用: {}", availability.accessibility_available);
    println!("  - 系统准备状态: {}\n", availability.hybrid_ready);
    
    // 显示诊断信息
    println!("3. 诊断信息:");
    println!("{}\n", provider.get_diagnostics());
    
    // 实时测试
    println!("4. 实时上下文获取测试");
    println!("请在其他应用中输入一些文本，然后按回车键测试上下文获取...");
    println!("输入 'quit' 退出测试\n");
    
    loop {
        println!("按回车键获取当前活动窗口的上下文（或输入 'quit' 退出）:");
        
        let mut input = String::new();
        match io::stdin().read_line(&mut input) {
            Ok(_) => {
                let input = input.trim();
                if input == "quit" {
                    println!("退出测试");
                    break;
                }
                
                // 给用户一些时间切换到目标应用
                if input.is_empty() {
                    println!("3秒后开始获取上下文，请切换到目标应用...");
                    for i in (1..=3).rev() {
                        println!("{}...", i);
                        thread::sleep(Duration::from_secs(1));
                    }
                    
                    // 测试上下文获取
                    test_context_extraction(&mut provider);
                }
            }
            Err(e) => {
                error!("读取输入失败: {}", e);
                break;
            }
        }
    }
    
    println!("测试完成");
    Ok(())
}

fn test_context_extraction(provider: &mut HybridContextProvider) {
    println!("🔍 开始获取上下文...");
    
    let max_lengths = vec![100, 300, 500];
    
    for max_length in max_lengths {
        println!("\n--- 测试最大长度: {} ---", max_length);
        
        match provider.get_context_before_cursor(0, None, max_length) {
            Ok(context) if !context.trim().is_empty() => {
                println!("✓ 成功获取上下文 (使用方法: {})", provider.get_last_method_used());
                println!("  长度: {} 字符", context.len());
                println!("  内容预览: '{}'", 
                    context.chars().take(50).collect::<String>()
                );
                
                // 如果文本很长，显示更多信息
                if context.len() > 50 {
                    println!("  ... (共 {} 字符)", context.len());
                }
            }
            Ok(_) => {
                println!("⚠ 获取到空上下文 (使用方法: {})", provider.get_last_method_used());
            }
            Err(e) => {
                println!("✗ 上下文获取失败: {}", e);
                println!("  方法: {}", provider.get_last_method_used());
            }
        }
    }
    
    // 显示当前状态
    println!("\n📊 当前状态:");
    println!("{}", provider.get_diagnostics());
    
    // 测试方法切换
    println!("\n🔄 测试方法切换:");
    test_method_switching(provider);
}

fn test_method_switching(provider: &mut HybridContextProvider) {
    // 测试禁用TSF
    println!("  禁用TSF，仅使用Accessibility API...");
    provider.disable_tsf();
    
    match provider.get_context_before_cursor(0, None, 200) {
        Ok(context) if !context.trim().is_empty() => {
            println!("  ✓ 仅Accessibility API成功: {} 字符", context.len());
        }
        Ok(_) => {
            println!("  ⚠ 仅Accessibility API返回空内容");
        }
        Err(e) => {
            println!("  ✗ 仅Accessibility API失败: {}", e);
        }
    }
    
    // 重新启用TSF
    println!("  重新启用TSF...");
    provider.enable_tsf();
    
    // 测试禁用Accessibility
    println!("  禁用Accessibility API，仅使用TSF...");
    provider.disable_accessibility();
    
    match provider.get_context_before_cursor(0, None, 200) {
        Ok(context) if !context.trim().is_empty() => {
            println!("  ✓ 仅TSF成功: {} 字符", context.len());
        }
        Ok(_) => {
            println!("  ⚠ 仅TSF返回空内容");
        }
        Err(e) => {
            println!("  ✗ 仅TSF失败: {}", e);
        }
    }
    
    // 重新启用所有方法
    println!("  重新启用所有方法...");
    provider.enable_accessibility();
    
    println!("  方法切换测试完成");
}